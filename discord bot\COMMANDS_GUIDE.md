# 🤖 **<PERSON><PERSON> Bot** - Complete Commands Guide

## 📋 **Table of Contents**
- [🛡️ Admin Commands](#admin-commands)
- [👥 Public Commands](#public-commands)
- [🎫 Support System](#support-system)
- [🏆 Economy & Leveling](#economy--leveling)
- [🎁 Fun & Entertainment](#fun--entertainment)
- [⚙️ Configuration](#configuration)

---

## 🛡️ **Admin Commands**
*Requires special permissions - Only admins and moderators can see these*

### 🔨 **Moderation**
| Command | Description | Permission Required |
|---------|-------------|-------------------|
| `/mod ban <user> [reason]` | Ban a user from the server | Ban Members |
| `/mod kick <user> [reason]` | Kick a user from the server | Kick Members |
| `/mod mute <user> [duration] [reason]` | Timeout a user | Moderate Members |
| `/mod unmute <user>` | Remove timeout from user | Moderate Members |
| `/mod warn <user> [reason]` | Warn a user | Moderate Members |
| `/mod warnings [user]` | View user warnings | Moderate Members |
| `/mod purge <amount>` | Delete multiple messages | Manage Messages |

### 🛡️ **Advanced Moderation**
| Command | Description | Permission Required |
|---------|-------------|-------------------|
| `/advancedmod slowmode <seconds>` | Set channel slowmode | Moderate Members |
| `/advancedmod nuke [reason]` | Delete and recreate channel | Moderate Members |
| `/advancedmod tempban <user> <duration>` | Temporary ban user | Moderate Members |
| `/advancedmod massban <users>` | Ban multiple users | Moderate Members |

### 🛡️ **Security & Anti-Spam**
| Command | Description | Permission Required |
|---------|-------------|-------------------|
| `/antispam enable` | Enable anti-spam system | Administrator |
| `/antispam disable` | Disable anti-spam system | Administrator |
| `/antispam status` | Check anti-spam status | Administrator |
| `/verification setup <channel> <role>` | Setup verification system | Manage Guild |
| `/verification enable` | Enable verification | Manage Guild |
| `/verification disable` | Disable verification | Manage Guild |

### ⚙️ **Server Configuration**
| Command | Description | Permission Required |
|---------|-------------|-------------------|
| `/settings view` | View current server settings | Manage Guild |
| `/settings prefix <prefix>` | Change bot prefix | Manage Guild |
| `/logging setup` | Setup server logging | Manage Guild |
| `/logging enable <type>` | Enable specific logging | Manage Guild |
| `/logging disable <type>` | Disable specific logging | Manage Guild |

### 🎭 **Role Management**
| Command | Description | Permission Required |
|---------|-------------|-------------------|
| `/autorole add <role>` | Add auto role for new members | Manage Roles |
| `/autorole remove <role>` | Remove auto role | Manage Roles |
| `/autorole list` | List all auto roles | Manage Roles |
| `/autorole toggle <enabled>` | Enable/disable auto roles | Manage Roles |
| `/reactionroles create` | Create reaction role message | Manage Roles |
| `/reactionroles add` | Add reaction to existing message | Manage Roles |
| `/reactionroles list` | List all reaction roles | Manage Roles |

### 🎁 **Events & Giveaways**
| Command | Description | Permission Required |
|---------|-------------|-------------------|
| `/giveaway start` | Start a new giveaway | Manage Messages |
| `/giveaway end <message_id>` | End giveaway early | Manage Messages |
| `/giveaway reroll <message_id>` | Reroll giveaway winners | Manage Messages |
| `/giveaway list` | List active giveaways | Manage Messages |
| `/giveaway delete <message_id>` | Delete a giveaway | Manage Messages |

### 🎫 **Ticket System**
| Command | Description | Permission Required |
|---------|-------------|-------------------|
| `/ticket setup [channel]` | Setup ticket system | Manage Channels |
| `/ticket stats` | View ticket statistics | Manage Channels |
| `/ticket close` | Close current ticket | Manage Channels |
| `/ticket add <user>` | Add user to ticket | Manage Channels |
| `/ticket remove <user>` | Remove user from ticket | Manage Channels |

### 👋 **Welcome System**
| Command | Description | Permission Required |
|---------|-------------|-------------------|
| `/welcome setup <channel>` | Setup welcome system | Manage Guild |
| `/welcome message <type> <message>` | Set welcome/goodbye message | Manage Guild |
| `/welcome autorole [role]` | Set auto role for new members | Manage Guild |
| `/welcome enable <feature>` | Enable welcome features | Manage Guild |
| `/welcome disable <feature>` | Disable welcome features | Manage Guild |
| `/welcome test <type>` | Test welcome messages | Manage Guild |
| `/welcome status` | View welcome configuration | Manage Guild |

---

## 👥 **Public Commands**
*Available to all users - No special permissions required*

### ⚙️ **Utility & Information**
| Command | Description |
|---------|-------------|
| `/util userinfo [user]` | Get detailed user information |
| `/util serverinfo` | Get detailed server information |
| `/util avatar [user]` | Get user's avatar in high quality |
| `/util ping` | Check bot latency and response time |
| `/util help` | Show main help menu |
| `/util help category:admin` | Show admin commands |
| `/util help category:public` | Show public commands |

### 🏆 **Leveling & Rankings**
| Command | Description |
|---------|-------------|
| `/level rank [user]` | Check your or someone's rank |
| `/level card [user]` | Get beautiful rank card |
| `/leaderboard` | View server XP leaderboard |
| `/leaderboard top` | View top 10 users |

### 💰 **Economy System**
| Command | Description |
|---------|-------------|
| `/economy balance [user]` | Check coin balance |
| `/economy daily` | Claim daily reward |
| `/economy work` | Work to earn coins |
| `/economy shop` | View the coin shop |
| `/economy buy <item>` | Buy items from shop |
| `/economy transfer <user> <amount>` | Send coins to another user |
| `/economy leaderboard` | View richest users |

### 🎁 **Fun & Entertainment**
| Command | Description |
|---------|-------------|
| `/fun meme` | Get random memes |
| `/fun joke` | Get random jokes |
| `/fun 8ball <question>` | Ask the magic 8-ball |
| `/fun coinflip` | Flip a coin |
| `/fun dice [sides]` | Roll a dice |
| `/fun rps <choice>` | Play rock paper scissors |
| `/fun trivia` | Get trivia questions |
| `/fun quote` | Get inspirational quotes |
| `/fun cat` | Get random cat images |
| `/fun dog` | Get random dog images |

---

## 🎯 **Command Categories Overview**

### 🔒 **Admin-Only Commands (11 categories):**
1. **Moderation** - Ban, kick, mute, warn users
2. **Advanced Moderation** - Slowmode, nuke, tempban
3. **Anti-Spam** - Security and spam protection
4. **Settings** - Server configuration
5. **Logging** - Activity logging setup
6. **Auto Roles** - Automatic role assignment
7. **Reaction Roles** - Role assignment via reactions
8. **Giveaways** - Event management
9. **Tickets** - Support system management
10. **Welcome** - Welcome/goodbye system
11. **Verification** - Member verification

### 🌍 **Public Commands (4 categories):**
1. **Utility** - Information and help commands
2. **Leveling** - XP and ranking system
3. **Economy** - Coin system and shop
4. **Fun** - Entertainment and games

---

## 📊 **Permission Requirements**

| Permission | Commands |
|------------|----------|
| **Administrator** | Anti-spam system |
| **Manage Guild** | Settings, logging, welcome, verification |
| **Manage Channels** | Ticket system |
| **Manage Roles** | Auto roles, reaction roles |
| **Manage Messages** | Giveaways, purge |
| **Moderate Members** | Moderation, advanced moderation |
| **Ban Members** | Ban command |
| **Kick Members** | Kick command |

---

## 🎨 **Features & Highlights**

### ✨ **Beautiful Design**
- 🎨 Custom emojis throughout all commands
- 🖼️ Rich embeds with server branding
- 🌈 Color-coded categories and responses
- 📱 Mobile-friendly layouts

### 🔧 **Advanced Functionality**
- 🛡️ Comprehensive permission system
- 📊 Real-time statistics and analytics
- 💾 Persistent data storage
- 🔄 Automatic command registration

### 🎯 **User Experience**
- 📋 Organized help system with categories
- 🔍 Easy command discovery
- ⚡ Fast response times
- 🎪 Interactive elements and buttons

---

## 🚀 **Getting Started**

1. **For Admins:** Start with `/util help category:admin` to see all admin commands
2. **For Users:** Use `/util help category:public` to see available commands
3. **Setup:** Use `/ticket setup`, `/welcome setup`, `/logging setup` for initial configuration
4. **Help:** Use `/util help` anytime for the main command center

---

**🎉 Powered by Shanta Somali • 🟢 Online & Ready**
