const { Client, GatewayIntentBits, Collection, Events, InteractionType, PermissionFlagsBits, EmbedBuilder } = require('discord.js');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

console.log('🚀 Starting Discord Moderation Bot...');
console.log('📦 Loading dependencies...');

// Import development monitoring
const DevMonitoring = require('./utils/devMonitoring');

// Import anti-spam system
const AntiSpamSystem = require('./utils/antiSpam');

// Import economy system
const EconomySystem = require('./utils/economySystem');

// Import reaction role system
const ReactionRoleSystem = require('./utils/reactionRoleSystem');

// Import auto role system
const AutoRoleSystem = require('./utils/autoRoleSystem');

// Import emoji manager
const EmojiManager = require('./utils/emojiManager');

// Import commands display
const CommandsDisplay = require('./utils/commandsDisplay');

// Import update notifications
const UpdateNotifications = require('./utils/updateNotifications');



// Enhanced logging function with analytics
async function logActivity(type, message, user = null, guild = null) {
    const timestamp = new Date().toLocaleTimeString();
    const userInfo = user ? `[${user.tag}]` : '';
    const guildInfo = guild ? `[${guild.name}]` : '';
    console.log(`[${timestamp}] ${type} ${guildInfo} ${userInfo} ${message}`);

    // Log to database for analytics
    if (guild && user) {
        try {
            const Database = require('./database/database');
            await Database.logAnalytics(guild.id, type.toLowerCase().replace(/[^a-z0-9]/g, '_'), { action: message, user_tag: user.tag }, user.id);
        } catch (error) {
            // Silently fail analytics logging
        }
    }
}

// Make logActivity globally available
global.logActivity = logActivity;

// Create a new client instance
const client = new Client({
    intents: [
        GatewayIntentBits.Guilds,
        GatewayIntentBits.GuildMessages,
        GatewayIntentBits.MessageContent,
        GatewayIntentBits.GuildMembers,
        GatewayIntentBits.GuildModeration,
        GatewayIntentBits.GuildMessageReactions,
        GatewayIntentBits.DirectMessages
    ]
});

// Collections for commands and cooldowns
client.commands = new Collection();
client.cooldowns = new Collection();

// Initialize development monitoring
const devMonitoring = new DevMonitoring(client);

// Initialize anti-spam system
const antiSpamSystem = new AntiSpamSystem();

// Initialize economy system
const economySystem = new EconomySystem();

// Initialize reaction role system
const reactionRoleSystem = new ReactionRoleSystem(client);

// Initialize auto role system
const autoRoleSystem = new AutoRoleSystem(client);

// Make systems globally available
global.antiSpamSystem = antiSpamSystem;
global.economySystem = economySystem;
global.reactionRoleSystem = reactionRoleSystem;
global.autoRoleSystem = autoRoleSystem;

client.antiSpamSystem = antiSpamSystem;
client.economySystem = economySystem;
client.reactionRoleSystem = reactionRoleSystem;
client.autoRoleSystem = autoRoleSystem;

// Initialize emoji manager
const emojiManager = new EmojiManager(client);

// Initialize commands display
const commandsDisplay = new CommandsDisplay(client);

// Initialize update notifications
const updateNotifications = new UpdateNotifications(client);

// Make emoji manager globally available
global.emojiManager = emojiManager;

// Import and make emoji replacer globally available
const emojiReplacer = require('./utils/emojiReplacer');
global.emojiReplacer = emojiReplacer;

// Import and make embed builder globally available
const CustomEmbedBuilder = require('./utils/embedBuilder');
const embedBuilder = new CustomEmbedBuilder();
global.embedBuilder = embedBuilder;

// Import advanced ticket system
const AdvancedTicketSystem = require('./utils/advancedTicketSystem');
const advancedTicketSystem = new AdvancedTicketSystem(client);



// Helper function to log changes
async function logBotChange(version, changes, type = 'update') {
    try {
        await devMonitoring.logChangelog(version, changes, type);
        // Also send to update notifications channel
        await updateNotifications.sendUpdateNotification(version, changes, type);
    } catch (error) {
        console.error('Failed to log changelog:', error);
    }
}

// Make it globally accessible
global.logBotChange = logBotChange;



// Load commands (both prefix and slash commands)
const commandsPath = path.join(__dirname, 'commands');
if (fs.existsSync(commandsPath)) {
    const commandFiles = fs.readdirSync(commandsPath).filter(file => file.endsWith('.js'));

    for (const file of commandFiles) {
        const filePath = path.join(commandsPath, file);
        const command = require(filePath);

        if ('data' in command && 'execute' in command) {
            // Handle both SlashCommandBuilder and regular commands
            let commandName;
            if (command.data.name) {
                commandName = command.data.name;
            } else if (command.data.toJSON && command.data.toJSON().name) {
                commandName = command.data.toJSON().name;
            } else if (command.slashData && command.slashData.name) {
                commandName = command.slashData.name;
            }

            if (commandName) {
                client.commands.set(commandName, command);
                // console.log(`📝 Loaded command: ${commandName}`); // Hidden to reduce logs
            }
        } else {
            console.log(`[WARNING] The command at ${filePath} is missing a required "data" or "execute" property.`);
        }
    }
}

// Load events
const eventsPath = path.join(__dirname, 'events');
if (fs.existsSync(eventsPath)) {
    const eventFiles = fs.readdirSync(eventsPath).filter(file => file.endsWith('.js'));

    for (const file of eventFiles) {
        const filePath = path.join(eventsPath, file);
        const event = require(filePath);

        if (event.once) {
            client.once(event.name, (...args) => event.execute(...args));
        } else {
            client.on(event.name, (...args) => event.execute(...args));
        }
    }
}

// Initialize database
console.log('🔧 Initializing database...');
const Database = require('./database/database');
Database.init();

// Ready event
client.once(Events.ClientReady, async () => {
    console.log(`🎉 Ready! Logged in as ${client.user.tag}`);
    console.log(`📊 Bot is in ${client.guilds.cache.size} servers`);
    console.log(`👥 Serving ${client.users.cache.size} users`);
    console.log('🔍 LIVE MONITORING ACTIVE - All bot activities will be logged below:');
    console.log('='.repeat(60));

    // Initialize emoji manager
    await emojiManager.initialize();

    // Initialize development monitoring
    await devMonitoring.initialize();

    // Initialize anti-spam system
    await antiSpamSystem.initialize(client);

    // Initialize economy system
    await economySystem.initialize(client);

    // Initialize commands display
    await commandsDisplay.initialize();

    // Initialize update notifications
    await updateNotifications.initialize();

    // Only send changelog on actual updates (not every restart)
    // Uncomment below when you want to send an update notification:
    // await logBotChange('v1.8.1', [
    //     'Added commands display channel system',
    //     'Added update notifications channel',
    //     'Improved bot restart behavior',
    //     'Fixed commands message persistence'
    // ], 'update');



    // Register slash commands
    await registerSlashCommands();

    client.user.setActivity('Moderating servers', { type: 3 }); // 3 = WATCHING
    logActivity('🟢 READY', 'Bot fully initialized and monitoring started');
});

// Function to register slash commands
async function registerSlashCommands() {
    const { REST, Routes } = require('discord.js');

    try {
        console.log('🔄 Registering slash commands...');

        const commands = [];

        // Collect all slash commands
        for (const [name, command] of client.commands) {
            if (command.slashData && command.slashData.toJSON) {
                commands.push(command.slashData.toJSON());
                // console.log(`📝 Prepared slash command: ${command.slashData.name}`); // Hidden to reduce logs
            } else if (command.data && command.data.toJSON) {
                // Support for new command structure
                commands.push(command.data.toJSON());
                // console.log(`📝 Prepared slash command: ${command.data.name}`); // Hidden to reduce logs
            }
        }

        if (commands.length === 0) {
            console.log('⚠️ No slash commands found to register');
            return;
        }

        const rest = new REST().setToken(process.env.DISCORD_TOKEN);
        const clientId = client.user.id;

        // Register globally (takes 1-3 hours to propagate)
        const globalData = await rest.put(
            Routes.applicationCommands(clientId),
            { body: commands }
        );

        console.log(`✅ Successfully registered ${globalData.length} global slash commands!`);

        logActivity('✅ COMMANDS', `Registered ${globalData.length} slash commands globally`);

    } catch (error) {
        console.error('❌ Failed to register slash commands:', error);
        logActivity('❌ COMMANDS', `Failed to register slash commands: ${error.message}`);
    }
}



// Interaction handling for buttons and slash commands
client.on('interactionCreate', async interaction => {
    if (interaction.type === InteractionType.ApplicationCommand) {
        // Handle slash commands
        const command = client.commands.get(interaction.commandName);
        if (!command) {
            logActivity('❌ COMMAND', `Unknown command: /${interaction.commandName}`, interaction.user, interaction.guild);
            return;
        }

        logActivity('⚡ SLASH', `/${interaction.commandName} executed`, interaction.user, interaction.guild);

        try {
            await command.execute(interaction);
            logActivity('✅ SUCCESS', `/${interaction.commandName} completed successfully`, interaction.user, interaction.guild);
        } catch (error) {
            console.error('Slash command error:', error);
            logActivity('❌ ERROR', `/${interaction.commandName} failed: ${error.message}`, interaction.user, interaction.guild);

            // Log to development monitoring
            await devMonitoring.logError(error, `Slash command: /${interaction.commandName}`);

            // Only try to respond if interaction hasn't been handled yet
            try {
                if (!interaction.replied && !interaction.deferred) {
                    await interaction.reply({
                        content: '❌ There was an error executing this command!',
                        flags: 64
                    });
                } else if (interaction.deferred && !interaction.replied) {
                    await interaction.editReply({
                        content: '❌ There was an error executing this command!'
                    });
                } else if (interaction.replied) {
                    await interaction.followUp({
                        content: '❌ There was an error executing this command!',
                        flags: 64
                    });
                }
            } catch (replyError) {
                // Only log if it's not an "already acknowledged" error
                if (!replyError.message.includes('already been acknowledged')) {
                    console.error('Failed to send error reply:', replyError);
                }
            }
        }
    } else if (interaction.isButton()) {
        // Handle button interactions
        logActivity('🔘 BUTTON', `Button clicked: ${interaction.customId}`, interaction.user, interaction.guild);

        // Check if this interaction needs a modal or should not be deferred
        const needsModal = interaction.customId.startsWith('advanced_ticket_') &&
                          interaction.customId.includes('whitelist');

        const noDefer = interaction.customId === 'claim_ticket' ||
                       interaction.customId === 'unclaim_ticket' ||
                       interaction.customId === 'add_user_ticket' ||
                       interaction.customId === 'delete_ticket' ||
                       interaction.customId === 'giveaway_enter' ||
                       interaction.customId === 'verify_user' ||
                       interaction.customId.startsWith('whitelist_reject_') ||
                       interaction.customId.startsWith('whitelist_accept_');

        // DEFER only if not showing modal, not in no-defer list, and interaction is still valid
        if (!needsModal && !noDefer) {
            try {
                // Check if interaction is still valid (not expired)
                if (Date.now() - interaction.createdTimestamp < 2900000) { // 2.9 seconds safety margin
                    await interaction.deferReply({ flags: 64 });
                } else {
                    console.log('⚠️ Interaction too old, skipping defer');
                    return;
                }
            } catch (deferError) {
                // Only log non-timeout errors
                if (deferError.code !== 10062 && deferError.code !== 40060 &&
                    !deferError.message.includes('Unknown interaction')) {
                    console.log('⚠️ Failed to defer interaction:', deferError.message);
                }
                return; // Exit early if we can't defer
            }
        }

        // Anti-abuse detection for rapid button clicking
        const userId = interaction.user.id;
        const now = Date.now();

        if (!client.buttonSpam) client.buttonSpam = new Map();

        if (!client.buttonSpam.has(userId)) {
            client.buttonSpam.set(userId, { count: 1, lastClick: now });
        } else {
            const userData = client.buttonSpam.get(userId);
            if (now - userData.lastClick < 5000) { // 5 seconds
                userData.count++;
                userData.lastClick = now;

                if (userData.count > 10) { // More than 10 clicks in 5 seconds
                    await devMonitoring.logAntiAbuse('Button Spam', interaction.user, interaction.guild,
                        `Rapid button clicking detected: ${userData.count} clicks in 5 seconds`);
                    return;
                }
            } else {
                userData.count = 1;
                userData.lastClick = now;
            }
        }



        try {
            // Advanced ticket system handlers
            if (interaction.customId.startsWith('advanced_ticket_')) {
                const ticketType = interaction.customId.replace('advanced_ticket_', '');

                if (ticketType === 'whitelist') {
                    await advancedTicketSystem.handleWhitelistApplication(interaction);
                } else if (ticketType === 'general') {
                    await advancedTicketSystem.handleGeneralTicket(interaction);
                } else if (ticketType === 'ban_appeal') {
                    await advancedTicketSystem.handleBanAppeal(interaction);
                } else if (ticketType === 'report') {
                    await advancedTicketSystem.handleReportTicket(interaction);
                } else {
                    await interaction.editReply({
                        content: `${global.emojiReplacer ? global.emojiReplacer.getError() : '❌'} Unknown ticket type: ${ticketType}`
                    });
                }
            }
            // Whitelist decision handlers
            else if (interaction.customId.startsWith('whitelist_accept_')) {
                const applicationId = interaction.customId.replace('whitelist_accept_', '');
                await advancedTicketSystem.handleWhitelistDecision(interaction, 'accept', applicationId);
            }
            else if (interaction.customId.startsWith('whitelist_reject_')) {
                const applicationId = interaction.customId.replace('whitelist_reject_', '');
                await advancedTicketSystem.handleWhitelistDecision(interaction, 'reject', applicationId);
            }
            else if (interaction.customId.startsWith('whitelist_info_')) {
                // Handle "More Info" button - just acknowledge for now
                await interaction.editReply({
                    content: `${global.emojiReplacer ? global.emojiReplacer.getInfo() : 'ℹ️'} More info feature coming soon! For now, you can contact the applicant directly.`
                });
            }
            // Private channel close handler
            else if (interaction.customId === 'close_private_channel') {
                await interaction.editReply({
                    content: `${global.emojiReplacer ? global.emojiReplacer.getSuccess() : '✅'} Closing private channel in 5 seconds...`
                });
                setTimeout(async () => {
                    try {
                        await interaction.channel.delete('Private channel closed by user');
                    } catch (error) {
                        console.error('Failed to close private channel:', error);
                    }
                }, 5000);
            }
            // Extend channel time handler
            else if (interaction.customId === 'extend_channel_time') {
                // Check if user has admin/mod permissions
                const hasPermission = interaction.member.permissions.has(PermissionFlagsBits.ManageChannels) ||
                                    interaction.member.permissions.has(PermissionFlagsBits.Administrator) ||
                                    interaction.member.permissions.has(PermissionFlagsBits.ManageMessages);

                if (!hasPermission) {
                    return await interaction.editReply({
                        content: `${global.emojiReplacer ? global.emojiReplacer.getError() : '❌'} Only staff members can extend channel time.`
                    });
                }

                await interaction.editReply({
                    content: `${global.emojiReplacer ? global.emojiReplacer.getSuccess() : '✅'} Channel time extended by 12 hours! This channel will now stay open longer.`
                });

                // Send notification to channel
                const extendEmbed = new EmbedBuilder()
                    .setColor(0x00FF00)
                    .setTitle(`${global.emojiReplacer ? global.emojiReplacer.getTime() : '⏰'} Channel Time Extended`)
                    .setDescription(`Channel time has been extended by **12 hours** by ${interaction.user}.\n\nThis gives you more time to get help and ask questions!`)
                    .setTimestamp();

                await interaction.followUp({ embeds: [extendEmbed] });
            }
            // Giveaway entry button
            else if (interaction.customId === 'giveaway_enter') {
                try {
                    // Check if user already reacted
                    const reaction = interaction.message.reactions.cache.get('🎉');
                    let alreadyEntered = false;

                    if (reaction) {
                        const users = await reaction.users.fetch();
                        alreadyEntered = users.has(interaction.user.id);
                    }

                    if (alreadyEntered) {
                        await interaction.reply({
                            content: `${global.emojiReplacer ? global.emojiReplacer.getInfo() : 'ℹ️'} You are already entered in this giveaway!`,
                            flags: 64
                        });
                        return;
                    }

                    // Add reaction to enter giveaway
                    await interaction.message.react('🎉');

                    await interaction.reply({
                        content: `${global.emojiReplacer ? global.emojiReplacer.getSuccess() : '✅'} You have entered the giveaway! Good luck!`,
                        flags: 64
                    });

                    // Log activity
                    if (global.logActivity) {
                        global.logActivity('🎉 GIVEAWAY', `Entered giveaway via button`, interaction.user, interaction.guild);
                    }

                } catch (error) {
                    console.error('Failed to enter giveaway:', error);

                    try {
                        await interaction.reply({
                            content: `${global.emojiReplacer ? global.emojiReplacer.getError() : '❌'} Failed to enter giveaway. Please try reacting with 🎉 instead.`,
                            flags: 64
                        });
                    } catch (replyError) {
                        // Only log if it's not an "already acknowledged" error
                        if (!replyError.message.includes('already been acknowledged')) {
                            console.error('Failed to send giveaway error reply:', replyError);
                        }
                    }
                }
            }
            // Verification button
            else if (interaction.customId === 'verify_user') {
                try {
                    const Database = require('./database/database');
                    const settings = await Database.getVerificationSettings(interaction.guild.id);

                    if (!settings || !settings.verification_enabled) {
                        return await interaction.reply({
                            content: `${global.emojiReplacer ? global.emojiReplacer.getError() : '❌'} Verification system is not enabled.`,
                            flags: 64
                        });
                    }

                    const role = interaction.guild.roles.cache.get(settings.verified_role);
                    if (!role) {
                        return await interaction.reply({
                            content: `${global.emojiReplacer ? global.emojiReplacer.getError() : '❌'} Verification role not found.`,
                            flags: 64
                        });
                    }

                    if (interaction.member.roles.cache.has(role.id)) {
                        return await interaction.reply({
                            content: `${global.emojiReplacer ? global.emojiReplacer.getInfo() : 'ℹ️'} You are already verified!`,
                            flags: 64
                        });
                    }

                    await interaction.member.roles.add(role, 'User verified via button');

                    await interaction.reply({
                        content: `${global.emojiReplacer ? global.emojiReplacer.getSuccess() : '✅'} **Welcome to the server!**\n\nYou have been verified and now have access to all channels. Enjoy your stay!`,
                        flags: 64
                    });

                    // Log verification
                    if (global.logActivity) {
                        global.logActivity('🛡️ VERIFICATION', `User verified via button`, interaction.user, interaction.guild);
                    }

                } catch (error) {
                    console.error('Failed to verify user:', error);
                    await interaction.reply({
                        content: `${global.emojiReplacer ? global.emojiReplacer.getError() : '❌'} Failed to verify. Please contact an administrator.`,
                        flags: 64
                    });
                }
            }
            // Legacy ticket system handlers (exclude ticket_priority)
            else if (interaction.customId.startsWith('ticket_') && interaction.customId !== 'ticket_priority') {
                const TicketSystem = require('./utils/ticketSystem');
                await TicketSystem.handleTicketButton(interaction);
            }
            else if (interaction.customId === 'close_ticket') {
                const TicketSystem = require('./utils/ticketSystem');
                await TicketSystem.handleCloseTicket(interaction);
            }
            else if (interaction.customId === 'delete_ticket') {
                const TicketSystem = require('./utils/ticketSystem');
                await TicketSystem.handleDeleteTicket(interaction);
            }
            else if (interaction.customId === 'add_user_ticket') {
                // Check if this is a closed ticket
                if (interaction.channel.name.startsWith('closed-')) {
                    return await interaction.reply({
                        content: '❌ This ticket is closed. Buttons are disabled.',
                        ephemeral: true
                    });
                }

                // Check if user has ticket helper roles or admin permissions
                const RoleHelper = require('./utils/roleHelper');
                const hasTicketPermissions = RoleHelper.hasTicketHelperRole(interaction.member);

                if (!hasTicketPermissions) {
                    return await interaction.reply({
                        content: '❌ Only ticket helpers and administrators can use this button.',
                        ephemeral: true
                    });
                }

                const TicketSystem = require('./utils/ticketSystem');
                await TicketSystem.handleAddUserToTicket(interaction);
            }
            else if (interaction.customId === 'claim_ticket') {
                // Check if this is a closed ticket
                if (interaction.channel.name.startsWith('closed-')) {
                    return await interaction.reply({
                        content: '❌ This ticket is closed. Buttons are disabled.',
                        ephemeral: true
                    });
                }

                // Check if user has ticket helper roles or admin permissions
                const RoleHelper = require('./utils/roleHelper');
                const hasTicketPermissions = RoleHelper.hasTicketHelperRole(interaction.member);

                if (!hasTicketPermissions) {
                    return await interaction.reply({
                        content: '❌ Only ticket helpers and administrators can use this button.',
                        ephemeral: true
                    });
                }

                const TicketSystem = require('./utils/ticketSystem');
                await TicketSystem.handleClaimTicket(interaction);
            }
            else if (interaction.customId === 'unclaim_ticket') {
                // Check if this is a closed ticket
                if (interaction.channel.name.startsWith('closed-')) {
                    return await interaction.reply({
                        content: '❌ This ticket is closed. Buttons are disabled.',
                        ephemeral: true
                    });
                }

                // Check if user has ticket helper roles or admin permissions
                const RoleHelper = require('./utils/roleHelper');
                const hasTicketPermissions = RoleHelper.hasTicketHelperRole(interaction.member);

                if (!hasTicketPermissions) {
                    return await interaction.reply({
                        content: '❌ Only ticket helpers and administrators can use this button.',
                        ephemeral: true
                    });
                }

                const TicketSystem = require('./utils/ticketSystem');
                await TicketSystem.handleUnclaimTicket(interaction);
            }
            else if (interaction.customId === 'ticket_priority') {
                // Handle mark urgent button
                const channel = interaction.channel;

                // Check if this is a ticket channel
                const isTicketChannel = channel.name.startsWith('ticket-') ||
                                       channel.name.startsWith('report-') ||
                                       channel.name.startsWith('ban-appeal-') ||
                                       channel.name.startsWith('general-') ||
                                       channel.name.startsWith('claimed-');

                if (!isTicketChannel) {
                    const replyMethod = interaction.deferred ? 'editReply' : 'reply';
                    return await interaction[replyMethod]({
                        content: '❌ This command can only be used in ticket channels.',
                        ephemeral: !interaction.deferred
                    });
                }

                // Check if user has ticket helper roles or admin permissions
                const RoleHelper = require('./utils/roleHelper');
                const hasTicketPermissions = RoleHelper.hasTicketHelperRole(interaction.member);

                if (!hasTicketPermissions) {
                    const replyMethod = interaction.deferred ? 'editReply' : 'reply';
                    return await interaction[replyMethod]({
                        content: '❌ Only ticket helpers and administrators can mark tickets as urgent.',
                        ephemeral: !interaction.deferred
                    });
                }

                try {
                    // Update channel topic to show urgent status
                    const currentTopic = channel.topic || '';
                    let newTopic;

                    if (currentTopic.includes('🚨 URGENT')) {
                        // Remove urgent status
                        newTopic = currentTopic.replace(' | 🚨 URGENT', '');

                        const replyMethod = interaction.deferred ? 'editReply' : 'reply';
                        await interaction[replyMethod]({
                            content: '✅ Ticket urgent status removed.',
                            ephemeral: !interaction.deferred
                        });
                    } else {
                        // Add urgent status
                        newTopic = `${currentTopic} | 🚨 URGENT`;

                        const replyMethod = interaction.deferred ? 'editReply' : 'reply';
                        await interaction[replyMethod]({
                            content: '🚨 Ticket marked as URGENT!',
                            ephemeral: !interaction.deferred
                        });

                        // Send urgent notification
                        const urgentEmbed = new EmbedBuilder()
                            .setColor(0xFF0000)
                            .setTitle('🚨 URGENT TICKET')
                            .setDescription(`This ticket has been marked as **URGENT** by ${interaction.user}.`)
                            .addFields(
                                { name: 'Marked By', value: `${interaction.user.tag}`, inline: true },
                                { name: 'Time', value: `<t:${Math.floor(Date.now() / 1000)}:F>`, inline: true }
                            )
                            .setTimestamp();

                        await channel.send({ embeds: [urgentEmbed] });
                    }

                    // Update channel topic
                    await channel.edit({ topic: newTopic });

                } catch (error) {
                    console.error('Error handling ticket priority:', error);
                    const replyMethod = interaction.deferred ? 'editReply' : 'reply';
                    if (!interaction.replied && !interaction.deferred) {
                        try {
                            await interaction.reply({
                                content: '❌ Failed to update ticket priority.',
                                ephemeral: true
                            });
                        } catch (replyError) {
                            console.error('Failed to send error reply:', replyError);
                        }
                    }
                }
            }

            logActivity('✅ BUTTON', `Button interaction completed: ${interaction.customId}`, interaction.user, interaction.guild);
        } catch (error) {
            // Only log actual errors, not common interaction timeouts
            if (error.code !== 10062 && error.code !== 40060 &&
                !error.message.includes('Unknown interaction') &&
                !error.message.includes('already been acknowledged')) {
                console.error('Button interaction error:', error);
                logActivity('❌ BUTTON', `Button interaction failed: ${interaction.customId} - ${error.message}`, interaction.user, interaction.guild);
            }

            // Don't try to respond to expired/acknowledged interactions
            if (error.code !== 10062 && error.code !== 40060) {
                try {
                    // Use appropriate reply method based on interaction state
                    if (interaction.deferred) {
                        await interaction.editReply({
                            content: '❌ There was an error processing this interaction!'
                        });
                    } else if (!interaction.replied) {
                        await interaction.reply({
                            content: '❌ There was an error processing this interaction!',
                            flags: 64 // EPHEMERAL
                        });
                    }
                } catch (replyError) {
                    // Suppress all common interaction errors
                    if (replyError.code !== 10062 && replyError.code !== 40060 &&
                        !replyError.message.includes('already been acknowledged') &&
                        !replyError.message.includes('Unknown interaction')) {
                        console.error('Failed to send button error reply:', replyError);
                    }
                }
            }
        }
    } else if (interaction.isModalSubmit()) {
        // Handle modal submissions
        logActivity('📝 MODAL', `Modal submitted: ${interaction.customId}`, interaction.user, interaction.guild);

        try {
            if (interaction.customId === 'whitelist_application_form') {
                await advancedTicketSystem.processWhitelistApplication(interaction);
            } else if (interaction.customId.startsWith('rejection_reason_')) {
                const applicationId = interaction.customId.replace('rejection_reason_', '');
                const reason = interaction.fields.getTextInputValue('rejection_reason');
                await advancedTicketSystem.handleRejectionReasonSubmission(interaction, applicationId, reason);
            } else if (interaction.customId === 'add_user_modal') {
                const TicketSystem = require('./utils/ticketSystem');
                await TicketSystem.processAddUserModal(interaction);
            }
            logActivity('✅ MODAL', `Modal submission completed: ${interaction.customId}`, interaction.user, interaction.guild);
        } catch (error) {
            console.error('Modal submission error:', error);
            logActivity('❌ MODAL', `Modal submission failed: ${interaction.customId} - ${error.message}`, interaction.user, interaction.guild);

            try {
                const errorEmoji = global.emojiReplacer ? global.emojiReplacer.getError() : '❌';
                if (!interaction.replied && !interaction.deferred) {
                    await interaction.reply({
                        content: `${errorEmoji} There was an error processing your application. Please try again.`,
                        flags: 64
                    });
                } else if (interaction.deferred && !interaction.replied) {
                    await interaction.editReply({
                        content: `${errorEmoji} There was an error processing your application. Please try again.`
                    });
                }
            } catch (replyError) {
                // Only log if it's not an "already acknowledged" error
                if (!replyError.message.includes('already been acknowledged')) {
                    console.error('Failed to send modal error reply:', replyError);
                }
            }
        }
    }
});

// Message logging, anti-spam checking, and economy
client.on('messageCreate', async message => {
    if (message.author.bot) return;

    // Multi-admin claim system - up to 3 admins can claim and respond to tickets
    // No restrictions on messaging in claimed tickets

    logActivity('💬 MESSAGE', `"${message.content.substring(0, 50)}${message.content.length > 50 ? '...' : ''}"`, message.author, message.guild);

    // Anti-spam system check
    try {
        const isSpam = await antiSpamSystem.checkMessage(message);
        if (isSpam) {
            logActivity('🛡️ ANTI-SPAM', `Spam detected and handled`, message.author, message.guild);
        }
    } catch (error) {
        console.error('Anti-spam system error:', error);
    }

    // Economy system - award coins for messages
    try {
        const coinsEarned = await economySystem.handleMessage(message);
        if (coinsEarned) {
            logActivity('💰 ECONOMY', `+${coinsEarned} coins for message`, message.author, message.guild);
        }
    } catch (error) {
        console.error('Economy system error:', error);
    }

    // Note: XP processing is handled in events/messageCreate.js to avoid duplication
});

// Guild events
client.on('guildCreate', async guild => {
    logActivity('🆕 GUILD', `Bot added to new server: ${guild.name}`, null, guild);
    await devMonitoring.logNewServer(guild);
});

client.on('guildDelete', async guild => {
    logActivity('❌ GUILD', `Bot removed from server: ${guild.name}`, null, guild);
    await devMonitoring.logServerRemove(guild);
});

// Guild member events
client.on('guildMemberAdd', member => {
    logActivity('👋 JOIN', `New member joined`, member.user, member.guild);
});

client.on('guildMemberRemove', member => {
    logActivity('👋 LEAVE', `Member left`, member.user, member.guild);
});

// Voice state events for economy system
client.on('voiceStateUpdate', async (oldState, newState) => {
    try {
        await economySystem.handleVoiceStateUpdate(oldState, newState);
    } catch (error) {
        console.error('Economy voice tracking error:', error);
    }
});

// Reaction events for reaction roles
client.on('messageReactionAdd', async (reaction, user) => {
    try {
        await reactionRoleSystem.handleReactionAdd(reaction, user);
    } catch (error) {
        console.error('Reaction role add error:', error);
    }
});

client.on('messageReactionRemove', async (reaction, user) => {
    try {
        await reactionRoleSystem.handleReactionRemove(reaction, user);
    } catch (error) {
        console.error('Reaction role remove error:', error);
    }
});

// Member join events for auto roles
client.on('guildMemberAdd', async (member) => {
    try {
        await autoRoleSystem.handleMemberJoin(member);
        logActivity('👋 MEMBER JOIN', `${member.user.tag} joined`, member.user, member.guild);
    } catch (error) {
        console.error('Auto role assignment error:', error);
    }
});

// Channel events
client.on('channelCreate', channel => {
    if (channel.guild) {
        logActivity('📁 CHANNEL', `Channel created: #${channel.name}`, null, channel.guild);
    }
});

client.on('channelDelete', channel => {
    if (channel.guild) {
        logActivity('🗑️ CHANNEL', `Channel deleted: #${channel.name}`, null, channel.guild);
    }
});

// Enhanced error handling with recovery
client.on('error', async (error) => {
    console.error('Discord client error:', error);

    // Check if it's a network-related error
    const networkErrors = ['ENOTFOUND', 'ECONNRESET', 'ETIMEDOUT', 'UND_ERR_CONNECT_TIMEOUT', 'ECONNREFUSED'];
    const isNetworkError = networkErrors.some(code => error.message.includes(code) || error.code === code);

    if (isNetworkError) {
        console.log('🌐 Network connectivity issue detected - implementing recovery measures');
        logActivity('🌐 NETWORK', `Internet connection issue: ${error.message}`);

        // Attempt automatic recovery for network issues
        setTimeout(async () => {
            try {
                if (client.readyAt) {
                    console.log('🔄 Network recovered, bot still connected');
                } else {
                    console.log('🔄 Attempting to reconnect...');
                    await loginWithRetry(3);
                }
            } catch (recoveryError) {
                console.error('❌ Recovery attempt failed:', recoveryError.message);
            }
        }, 5000);
    } else {
        logActivity('❌ CLIENT', `Discord error: ${error.message}`);
        try {
            await devMonitoring.logError(error, 'Discord client error');
        } catch (logError) {
            console.error('Failed to log error to monitoring:', logError.message);
        }
    }
});

client.on('warn', (warning) => {
    console.warn('Discord client warning:', warning);
    logActivity('⚠️ WARNING', `Discord warning: ${warning}`);
});

process.on('unhandledRejection', async error => {
    console.error('Unhandled promise rejection:', error);
    await devMonitoring.logError(error, 'Unhandled promise rejection');
});

process.on('uncaughtException', async error => {
    console.error('Uncaught exception:', error);
    await devMonitoring.logError(error, 'Uncaught exception');
    process.exit(1);
});

// Enhanced graceful shutdown with cleanup
async function performGracefulShutdown() {
    try {
        console.log('🔄 Shutting down gracefully...');
        logActivity('🔴 SHUTDOWN', 'Bot is shutting down gracefully');

        // Clear all timers and intervals
        if (global.economySystem && global.economySystem.voiceUpdateInterval) {
            clearInterval(global.economySystem.voiceUpdateInterval);
            console.log('🧹 Cleared economy system timers');
        }

        // Clear cooldowns and caches
        if (client.cooldowns) {
            client.cooldowns.clear();
            console.log('🧹 Cleared command cooldowns');
        }
        if (client.buttonSpam) {
            client.buttonSpam.clear();
            console.log('🧹 Cleared button spam protection');
        }

        // Close database connections
        console.log('💾 Closing database connections...');
        Database.close();

        // Destroy Discord client
        console.log('🔌 Disconnecting from Discord...');
        client.destroy();

        console.log('👋 Bot disconnected. Goodbye!');
        process.exit(0);
    } catch (error) {
        console.error('❌ Error during shutdown:', error);
        process.exit(1);
    }
}

process.on('SIGINT', performGracefulShutdown);
process.on('SIGTERM', performGracefulShutdown);

// Login to Discord with retry for poor internet
async function loginWithRetry(maxRetries = 3) {
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
        try {
            console.log(`🔐 Login attempt ${attempt}/${maxRetries}...`);
            await client.login(process.env.DISCORD_TOKEN);
            console.log('✅ Successfully logged in to Discord!');
            return;
        } catch (error) {
            console.error(`❌ Login attempt ${attempt} failed:`, error.message);

            // Check if it's a network-related error
            const networkErrors = ['ENOTFOUND', 'ECONNRESET', 'ETIMEDOUT', 'getaddrinfo'];
            const isNetworkError = networkErrors.some(code => error.message.includes(code));

            if (isNetworkError) {
                console.log('🌐 Network connectivity issue detected during login');
            }

            if (attempt < maxRetries) {
                const delay = attempt * 5000; // 5s, 10s, 15s delays
                console.log(`⏳ Retrying login in ${delay/1000} seconds...`);
                await new Promise(resolve => setTimeout(resolve, delay));
            } else {
                console.error('❌ All login attempts failed. Check your internet connection and try again.');
                process.exit(1);
            }
        }
    }
}

console.log('🔐 Logging in to Discord...');
loginWithRetry();
