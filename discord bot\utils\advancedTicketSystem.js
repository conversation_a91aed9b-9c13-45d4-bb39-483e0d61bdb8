const { Em<PERSON><PERSON><PERSON><PERSON>, ActionRowBuilder, ButtonBuilder, ButtonStyle, ChannelType, PermissionFlagsBits, ModalBuilder, TextInputBuilder, TextInputStyle } = require('discord.js');

class AdvancedTicketSystem {
    constructor(client) {
        this.client = client;
        this.activeTickets = new Map();
        this.whitelistApplications = new Map();
        this.ticketCounter = 1;
        this.whitelistCounter = 1;
        
        // Configuration
        this.config = {
            whitelistReviewChannelId: null, // Set this in setup
            whitelistRoleId: null, // Optional role to assign
            autoCloseTime: 24 * 60 * 60 * 1000, // 24 hours in milliseconds
            categories: {
                general: 'General Support',
                whitelist: 'Whitelist Applications',
                ban_appeal: 'Ban Appeals',
                bug_report: 'Bug Reports'
            }
        };
    }

    // Create advanced ticket panel with whitelist form
    createAdvancedTicketPanel() {
        const emojis = global.emojiReplacer || {};
        
        const embed = new EmbedBuilder()
            .setColor(0x0099FF)
            .setTitle(`${emojis.getTicket ? emojis.getTicket() : '🎫'} Advanced Support System`)
            .setDescription(`
**Welcome to our support system!**

Choose the type of assistance you need:

${emojis.getTicket ? emojis.getTicket() : '🎫'} **General Support** - Get help with server issues
${emojis.getSuccess ? emojis.getSuccess() : '✅'} **Whitelist Application** - Apply for server whitelist
${emojis.getWarning ? emojis.getWarning() : '⚠️'} **Ban Appeal** - Appeal a punishment
${emojis.getError ? emojis.getError() : '❌'} **Report Issue** - Report bugs or problems

*Only one ticket per user at a time.*
            `)
            .setThumbnail('https://cdn.discordapp.com/attachments/653195475201032202/1264287289517801493/logggopng.png')
            .setFooter({ text: 'Professional Support System • Response within 24 hours' })
            .setTimestamp();

        const buttons = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('advanced_ticket_general')
                    .setLabel('General Support')
                    .setEmoji(emojis.getTicket ? emojis.getTicket() : '🎫')
                    .setStyle(ButtonStyle.Primary),
                new ButtonBuilder()
                    .setCustomId('advanced_ticket_whitelist')
                    .setLabel('Whitelist Application')
                    .setEmoji(emojis.getSuccess ? emojis.getSuccess() : '✅')
                    .setStyle(ButtonStyle.Success),
                new ButtonBuilder()
                    .setCustomId('advanced_ticket_ban_appeal')
                    .setLabel('Ban Appeal')
                    .setEmoji(emojis.getWarning ? emojis.getWarning() : '⚠️')
                    .setStyle(ButtonStyle.Secondary)
            );

        const buttons2 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('advanced_ticket_report')
                    .setLabel('Report Issue')
                    .setEmoji(emojis.getError ? emojis.getError() : '❌')
                    .setStyle(ButtonStyle.Danger),
                new ButtonBuilder()
                    .setCustomId('ticket_stats')
                    .setLabel('System Stats')
                    .setEmoji(emojis.getChart ? emojis.getChart() : '📊')
                    .setStyle(ButtonStyle.Secondary)
            );

        return { embeds: [embed], components: [buttons, buttons2] };
    }

    // Handle general support ticket
    async handleGeneralTicket(interaction) {
        const emojis = global.emojiReplacer || {};

        // Check if user already has a ticket
        const existingTicket = interaction.guild.channels.cache.find(ch =>
            ch.name.startsWith('ticket-') && ch.topic && ch.topic.includes(interaction.user.id)
        );

        if (existingTicket) {
            return await interaction.editReply({
                content: `${emojis.getWarning ? emojis.getWarning() : '⚠️'} You already have an open ticket: ${existingTicket}`
            });
        }

        // Create ticket channel
        const ticketChannel = await this.createTicketChannel(interaction, 'general', 'General Support');

        await interaction.editReply({
            content: `${emojis.getSuccess ? emojis.getSuccess() : '✅'} **General Support Ticket Created!**\n\nYour ticket has been created: ${ticketChannel}\n\nOur staff will assist you shortly.`
        });
    }

    // Handle ban appeal ticket
    async handleBanAppeal(interaction) {
        const emojis = global.emojiReplacer || {};

        // Check if user already has a ticket
        const existingTicket = interaction.guild.channels.cache.find(ch =>
            ch.name.startsWith('appeal-') && ch.topic && ch.topic.includes(interaction.user.id)
        );

        if (existingTicket) {
            return await interaction.editReply({
                content: `${emojis.getWarning ? emojis.getWarning() : '⚠️'} You already have an open ban appeal: ${existingTicket}`
            });
        }

        // Create ban appeal channel
        const appealChannel = await this.createTicketChannel(interaction, 'ban_appeal', 'Ban Appeal');

        await interaction.editReply({
            content: `${emojis.getSuccess ? emojis.getSuccess() : '✅'} **Ban Appeal Created!**\n\nYour appeal has been created: ${appealChannel}\n\nPlease provide details about your ban and why it should be lifted.`
        });
    }

    // Handle report ticket
    async handleReportTicket(interaction) {
        const emojis = global.emojiReplacer || {};

        // Check if user already has a ticket
        const existingTicket = interaction.guild.channels.cache.find(ch =>
            ch.name.startsWith('report-') && ch.topic && ch.topic.includes(interaction.user.id)
        );

        if (existingTicket) {
            return await interaction.editReply({
                content: `${emojis.getWarning ? emojis.getWarning() : '⚠️'} You already have an open report: ${existingTicket}`
            });
        }

        // Create report channel
        const reportChannel = await this.createTicketChannel(interaction, 'report', 'Bug/Issue Report');

        await interaction.editReply({
            content: `${emojis.getSuccess ? emojis.getSuccess() : '✅'} **Report Ticket Created!**\n\nYour report has been created: ${reportChannel}\n\nPlease describe the bug or issue you encountered.`
        });
    }

    // Create ticket channel (general method)
    async createTicketChannel(interaction, type, displayName) {
        const emojis = global.emojiReplacer || {};
        const user = interaction.user;
        const guild = interaction.guild;

        // Generate channel name
        const channelName = `${type}-${user.username.toLowerCase().replace(/[^a-z0-9]/g, '')}-${Date.now().toString().slice(-4)}`;

        // Find staff roles - use role names for flexibility across servers
        const ticketHelperRoles = guild.roles.cache.filter(role =>
            role.name.toLowerCase().includes('ticket helper') ||
            role.name.toLowerCase().includes('support staff') ||
            role.name.toLowerCase().includes('ticket support') ||
            role.name.toLowerCase().includes('helper')
        );

        const adminRoles = guild.roles.cache.filter(role =>
            role.name.toLowerCase().includes('admin') ||
            role.name.toLowerCase().includes('owner') ||
            role.permissions.has(PermissionFlagsBits.Administrator)
        );

        // Combine ticket helpers and admins for staff mentions
        const allStaffRoles = new Map([...ticketHelperRoles, ...adminRoles]);

        // Create permission overwrites
        const permissionOverwrites = [
            {
                id: guild.roles.everyone.id,
                deny: [PermissionFlagsBits.ViewChannel]
            },
            {
                id: user.id,
                allow: [
                    PermissionFlagsBits.ViewChannel,
                    PermissionFlagsBits.SendMessages,
                    PermissionFlagsBits.ReadMessageHistory,
                    PermissionFlagsBits.AttachFiles,
                    PermissionFlagsBits.EmbedLinks
                ]
            },
            {
                id: guild.members.me.id,
                allow: [
                    PermissionFlagsBits.ViewChannel,
                    PermissionFlagsBits.SendMessages,
                    PermissionFlagsBits.ManageChannels,
                    PermissionFlagsBits.ReadMessageHistory
                ]
            }
        ];

        // Add ticket helper permissions first - with validation
        for (const role of ticketHelperRoles.values()) {
            if (role && role.id && guild.roles.cache.has(role.id)) {
                try {
                    permissionOverwrites.push({
                        id: role.id,
                        allow: [
                            PermissionFlagsBits.ViewChannel,
                            PermissionFlagsBits.SendMessages,
                            PermissionFlagsBits.ReadMessageHistory,
                            PermissionFlagsBits.AttachFiles,
                            PermissionFlagsBits.EmbedLinks,
                            PermissionFlagsBits.ManageMessages
                        ]
                    });
                } catch (error) {
                    console.error(`Failed to add ticket helper role permissions for ${role.name}:`, error);
                }
            }
        }

        // Add admin permissions - with validation
        for (const role of adminRoles.values()) {
            if (role && role.id && guild.roles.cache.has(role.id)) {
                try {
                    permissionOverwrites.push({
                        id: role.id,
                        allow: [
                            PermissionFlagsBits.ViewChannel,
                            PermissionFlagsBits.SendMessages,
                            PermissionFlagsBits.ReadMessageHistory,
                            PermissionFlagsBits.AttachFiles,
                            PermissionFlagsBits.EmbedLinks,
                            PermissionFlagsBits.ManageMessages
                        ]
                    });
                } catch (error) {
                    console.error(`Failed to add admin role permissions for ${role.name}:`, error);
                }
            }
        }

        // Get or create tickets category
        let category = guild.channels.cache.find(c => c.name === '🎫 Tickets' && c.type === ChannelType.GuildCategory);
        if (!category) {
            try {
                category = await guild.channels.create({
                    name: '🎫 Tickets',
                    type: ChannelType.GuildCategory,
                    permissionOverwrites: [
                        {
                            id: guild.roles.everyone.id,
                            deny: [PermissionFlagsBits.ViewChannel]
                        }
                    ]
                });
            } catch (error) {
                console.error('Failed to create tickets category:', error);
            }
        }

        // Create the channel with error handling
        let ticketChannel;
        try {
            ticketChannel = await guild.channels.create({
                name: channelName,
                type: ChannelType.GuildText,
                topic: `${displayName} ticket for ${user.tag} (${user.id})`,
                parent: category ? category.id : null,
                permissionOverwrites: permissionOverwrites
            });
        } catch (error) {
            console.error('Failed to create ticket channel with permissions:', error);

            // Try creating with minimal permissions if the full permission set fails
            try {
                ticketChannel = await guild.channels.create({
                    name: channelName,
                    type: ChannelType.GuildText,
                    topic: `${displayName} ticket for ${user.tag} (${user.id})`,
                    parent: category ? category.id : null,
                    permissionOverwrites: [
                        {
                            id: guild.roles.everyone.id,
                            deny: [PermissionFlagsBits.ViewChannel]
                        },
                        {
                            id: user.id,
                            allow: [
                                PermissionFlagsBits.ViewChannel,
                                PermissionFlagsBits.SendMessages,
                                PermissionFlagsBits.ReadMessageHistory
                            ]
                        },
                        {
                            id: guild.members.me.id,
                            allow: [
                                PermissionFlagsBits.ViewChannel,
                                PermissionFlagsBits.SendMessages,
                                PermissionFlagsBits.ManageChannels
                            ]
                        }
                    ]
                });
                console.log('✅ Created ticket channel with minimal permissions');
            } catch (fallbackError) {
                console.error('Failed to create ticket channel even with minimal permissions:', fallbackError);
                throw fallbackError;
            }
        }

        // Send welcome message
        const welcomeEmbed = new EmbedBuilder()
            .setColor(0x0099FF)
            .setTitle(`${emojis.getTicket ? emojis.getTicket() : '🎫'} ${displayName}`)
            .setDescription(`
**Hello ${user}!**

Thank you for creating a ${displayName.toLowerCase()}. Our staff team will assist you shortly.

${emojis.getInfo ? emojis.getInfo() : 'ℹ️'} **Please provide:**
${type === 'general' ? '• Detailed description of your issue\n• Steps you\'ve already tried\n• Any error messages you received' : ''}
${type === 'ban_appeal' ? '• Your username/ID when banned\n• Reason for the ban\n• Why you believe it should be lifted\n• What you\'ve learned from the experience' : ''}
${type === 'report' ? '• Detailed description of the bug/issue\n• Steps to reproduce the problem\n• Expected vs actual behavior\n• Screenshots if applicable' : ''}

${emojis.getTime ? emojis.getTime() : '⏰'} **Response Time:** Usually within 24 hours
${emojis.getAdmin ? emojis.getAdmin() : '👥'} **Staff:** Will be notified automatically
            `)
            .addFields(
                {
                    name: `${emojis.getGear ? emojis.getGear() : '⚙️'} Ticket Information`,
                    value: `**Type:** ${displayName}\n**Created:** <t:${Math.floor(Date.now() / 1000)}:F>\n**Status:** 🟢 Open`,
                    inline: false
                }
            )
            .setThumbnail(user.displayAvatarURL())
            .setFooter({ text: 'Support Ticket System • Professional Support' })
            .setTimestamp();

        // Create action buttons - add "Add User" button for non-whitelist tickets
        const actionButtons = new ActionRowBuilder();

        actionButtons.addComponents(
            new ButtonBuilder()
                .setCustomId('close_ticket')
                .setLabel('Close Ticket')
                .setEmoji(emojis.getError ? emojis.getError() : '🔒')
                .setStyle(ButtonStyle.Danger),
            new ButtonBuilder()
                .setCustomId('ticket_priority')
                .setLabel('Mark Urgent')
                .setEmoji(emojis.getWarning ? emojis.getWarning() : '⚠️')
                .setStyle(ButtonStyle.Secondary)
        );

        // Add "Add User" and "Claim" buttons for non-whitelist tickets
        if (type !== 'whitelist') {
            actionButtons.addComponents(
                new ButtonBuilder()
                    .setCustomId('add_user_ticket')
                    .setLabel('Add User')
                    .setEmoji(emojis.getUser ? emojis.getUser() : '➕')
                    .setStyle(ButtonStyle.Primary),
                new ButtonBuilder()
                    .setCustomId('claim_ticket')
                    .setLabel('Claim Ticket')
                    .setEmoji(emojis.getAdmin ? emojis.getAdmin() : '🔒')
                    .setStyle(ButtonStyle.Success)
            );
        }

        // Create staff mention string - prioritize ticket helpers (no actual mentions to prevent channel access)
        const staffNames = [];
        for (const role of ticketHelperRoles.values()) {
            if (role && role.id) {
                staffNames.push(role.name); // Use role name instead of mention
            }
        }
        for (const role of adminRoles.values()) {
            if (role && role.id) {
                staffNames.push(role.name); // Use role name instead of mention
            }
        }
        const staffMentionString = staffNames.length > 0 ? `📢 Staff notified: **${staffNames.join(', ')}**` : '📢 Staff will be notified';

        // Send single message with embed and buttons (everyone can see, permission-controlled)
        await ticketChannel.send({
            content: `${user}`,
            embeds: [welcomeEmbed],
            components: [actionButtons]
        });

        return ticketChannel;
    }

    // Handle whitelist application form
    async handleWhitelistApplication(interaction) {
        // Check if interaction is still valid (not expired)
        if (Date.now() - interaction.createdTimestamp > 2900000) { // 2.9 seconds safety margin
            console.log('⚠️ Interaction expired, cannot show modal');
            return;
        }

        const modal = new ModalBuilder()
            .setCustomId('whitelist_application_form')
            .setTitle('Whitelist Application Form');

        const discordNameInput = new TextInputBuilder()
            .setCustomId('discord_name')
            .setLabel('Discord Username')
            .setStyle(TextInputStyle.Short)
            .setPlaceholder('Enter your full Discord username (e.g., Username#1234)')
            .setRequired(true)
            .setMaxLength(50);

        const discordIdInput = new TextInputBuilder()
            .setCustomId('discord_id')
            .setLabel('Discord User ID')
            .setStyle(TextInputStyle.Short)
            .setPlaceholder('Right-click your name → Copy User ID')
            .setRequired(true)
            .setMaxLength(20);

        const fivemIdInput = new TextInputBuilder()
            .setCustomId('fivem_id')
            .setLabel('FiveM ID/License')
            .setStyle(TextInputStyle.Short)
            .setPlaceholder('Enter your FiveM identifier or license')
            .setRequired(true)
            .setMaxLength(100);

        const reasonInput = new TextInputBuilder()
            .setCustomId('whitelist_reason')
            .setLabel('Why do you want to join?')
            .setStyle(TextInputStyle.Paragraph)
            .setPlaceholder('Tell us why you want to be whitelisted on our server...')
            .setRequired(true)
            .setMaxLength(500);

        const experienceInput = new TextInputBuilder()
            .setCustomId('rp_experience')
            .setLabel('Roleplay Experience')
            .setStyle(TextInputStyle.Paragraph)
            .setPlaceholder('Describe your roleplay experience and background...')
            .setRequired(false)
            .setMaxLength(300);

        const firstActionRow = new ActionRowBuilder().addComponents(discordNameInput);
        const secondActionRow = new ActionRowBuilder().addComponents(discordIdInput);
        const thirdActionRow = new ActionRowBuilder().addComponents(fivemIdInput);
        const fourthActionRow = new ActionRowBuilder().addComponents(reasonInput);
        const fifthActionRow = new ActionRowBuilder().addComponents(experienceInput);

        modal.addComponents(firstActionRow, secondActionRow, thirdActionRow, fourthActionRow, fifthActionRow);

        try {
            await interaction.showModal(modal);
        } catch (error) {
            console.error('Failed to show whitelist modal:', error);

            // Only try to respond if it's not an "Unknown interaction" error
            if (error.code !== 10062) {
                try {
                    if (!interaction.replied && !interaction.deferred) {
                        await interaction.reply({
                            content: `${global.emojiReplacer ? global.emojiReplacer.getError() : '❌'} Failed to show application form. Please try again.`,
                            ephemeral: true
                        });
                    }
                } catch (replyError) {
                    // Only log if it's not an "already acknowledged" error
                    if (!replyError.message.includes('already been acknowledged')) {
                        console.error('Failed to send error reply:', replyError);
                    }
                }
            }
        }
    }

    // Process whitelist application submission
    async processWhitelistApplication(interaction) {
        const discordName = interaction.fields.getTextInputValue('discord_name');
        const discordId = interaction.fields.getTextInputValue('discord_id');
        const fivemId = interaction.fields.getTextInputValue('fivem_id');
        const reason = interaction.fields.getTextInputValue('whitelist_reason');
        const experience = interaction.fields.getTextInputValue('rp_experience') || 'Not provided';

        // Store application data
        const applicationId = `WL-${Date.now()}`;
        this.whitelistApplications.set(applicationId, {
            userId: interaction.user.id,
            discordName,
            discordId,
            fivemId,
            reason,
            experience,
            submittedAt: new Date(),
            status: 'pending'
        });

        // Send to review channel
        await this.sendToReviewChannel(interaction, applicationId, {
            discordName,
            discordId,
            fivemId,
            reason,
            experience
        });

        // Confirm submission to user
        const emojis = global.emojiReplacer || {};
        try {
            await interaction.reply({
                content: `${emojis.getSuccess ? emojis.getSuccess() : '✅'} **Application Submitted Successfully!**\n\nYour whitelist application has been submitted for review.\n**Application ID:** \`${applicationId}\`\n\nYou will receive a DM with the result within 24-48 hours.`,
                flags: 64
            });
        } catch (error) {
            console.error('Failed to confirm application submission:', error);
        }
    }

    // Send application to admin review channel
    async sendToReviewChannel(interaction, applicationId, data) {
        // Find or create review channel
        let reviewChannel = interaction.guild.channels.cache.find(c => c.name === 'whitelist-reviews');
        
        if (!reviewChannel) {
            reviewChannel = await interaction.guild.channels.create({
                name: 'whitelist-reviews',
                type: ChannelType.GuildText,
                permissionOverwrites: [
                    {
                        id: interaction.guild.roles.everyone.id,
                        deny: [PermissionFlagsBits.ViewChannel]
                    }
                ]
            });
        }

        const emojis = global.emojiReplacer || {};
        const embed = new EmbedBuilder()
            .setColor(0xFFD700)
            .setTitle(`${emojis.getTicket ? emojis.getTicket() : '📋'} New Whitelist Application`)
            .setDescription(`**Application ID:** \`${applicationId}\``)
            .addFields(
                { name: `${emojis.getUser ? emojis.getUser() : '👤'} Discord Name`, value: data.discordName, inline: true },
                { name: `${emojis.getUser ? emojis.getUser() : '🆔'} Discord ID`, value: data.discordId, inline: true },
                { name: `${emojis.getComputer ? emojis.getComputer() : '🎮'} FiveM ID`, value: data.fivemId, inline: true },
                { name: `${emojis.getReason ? emojis.getReason() : '📝'} Reason for Joining`, value: data.reason, inline: false },
                { name: `${emojis.getChart ? emojis.getChart() : '📈'} RP Experience`, value: data.experience, inline: false },
                { name: `${emojis.getTime ? emojis.getTime() : '🕒'} Submitted`, value: `<t:${Math.floor(Date.now() / 1000)}:F>`, inline: true }
            )
            .setThumbnail(interaction.user.displayAvatarURL())
            .setFooter({ text: 'Whitelist Application Review System' })
            .setTimestamp();

        const buttons = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId(`whitelist_accept_${applicationId}`)
                    .setLabel('Accept')
                    .setEmoji(emojis.getSuccess ? emojis.getSuccess() : '✅')
                    .setStyle(ButtonStyle.Success),
                new ButtonBuilder()
                    .setCustomId(`whitelist_reject_${applicationId}`)
                    .setLabel('Reject')
                    .setEmoji(emojis.getError ? emojis.getError() : '❌')
                    .setStyle(ButtonStyle.Danger),
                new ButtonBuilder()
                    .setCustomId(`whitelist_info_${applicationId}`)
                    .setLabel('More Info')
                    .setEmoji(emojis.getInfo ? emojis.getInfo() : 'ℹ️')
                    .setStyle(ButtonStyle.Secondary)
            );

        await reviewChannel.send({
            embeds: [embed],
            components: [buttons]
        });
    }

    // Handle whitelist application decision
    async handleWhitelistDecision(interaction, decision, applicationId) {
        // Try to defer interaction, but continue if it fails
        try {
            if (!interaction.replied && !interaction.deferred) {
                await interaction.deferReply({ ephemeral: true });
            }
        } catch (error) {
            // Interaction might already be handled, continue anyway
            console.log('Interaction already handled, continuing...');
        }

        const application = this.whitelistApplications.get(applicationId);
        if (!application) {
            const replyMethod = interaction.deferred ? 'editReply' : 'reply';
            return await interaction[replyMethod]({
                content: '❌ Application not found. This might happen if the bot was restarted. Please resubmit your application.',
                ephemeral: true
            });
        }

        // Check if already processed
        if (application.status && application.status !== 'pending') {
            const replyMethod = interaction.deferred ? 'editReply' : 'reply';
            return await interaction[replyMethod]({
                content: `✅ This application was already **${application.status.toUpperCase()}**. No further action needed.`,
                ephemeral: true
            });
        }

        const emojis = global.emojiReplacer || {};
        const user = await this.client.users.fetch(application.userId);

        if (decision === 'accept') {
            // Accept the application
            application.status = 'accepted';
            application.reviewedBy = interaction.user.id;
            application.reviewedAt = new Date();

            // Assign whitelist role if configured
            if (this.config.whitelistRoleId) {
                try {
                    const member = await interaction.guild.members.fetch(application.userId);
                    const role = interaction.guild.roles.cache.get(this.config.whitelistRoleId);
                    if (role && member) {
                        await member.roles.add(role);
                    }
                } catch (error) {
                    console.error('Failed to assign whitelist role:', error);
                }
            }

            // Create private channel for user
            const privateChannel = await this.createPrivateChannel(interaction.guild, user, applicationId);

            // Send acceptance DM
            try {
                const acceptEmbed = new EmbedBuilder()
                    .setColor(0x00FF00)
                    .setTitle(`${emojis.getSuccess ? emojis.getSuccess() : '🎉'} Whitelist Application Accepted!`)
                    .setDescription(`
**Congratulations!** Your whitelist application has been **ACCEPTED**.

**Application ID:** \`${applicationId}\`
**Reviewed by:** ${interaction.user.tag}
**Status:** ✅ Approved

You now have access to the whitelisted areas of our server.
A private channel has been created for you: ${privateChannel}

**Welcome to the community!** 🎉
                    `)
                    .setThumbnail('https://cdn.discordapp.com/attachments/653195475201032202/1264287289517801493/logggopng.png')
                    .setFooter({ text: 'Whitelist System • Congratulations!' })
                    .setTimestamp();

                await user.send({ embeds: [acceptEmbed] });
            } catch (error) {
                console.error('Failed to send acceptance DM:', error);
            }

            // Update review message
            await this.updateReviewMessage(interaction, applicationId, 'ACCEPTED', emojis.getSuccess ? emojis.getSuccess() : '✅');

        } else if (decision === 'reject') {
            // Reject the application
            application.status = 'rejected';
            application.reviewedBy = interaction.user.id;
            application.reviewedAt = new Date();

            // Send rejection DM
            try {
                const rejectEmbed = new EmbedBuilder()
                    .setColor(0xFF0000)
                    .setTitle(`${emojis.getError ? emojis.getError() : '❌'} Whitelist Application Rejected`)
                    .setDescription(`
Unfortunately, your whitelist application has been **REJECTED**.

**Application ID:** \`${applicationId}\`
**Reviewed by:** ${interaction.user.tag}
**Status:** ❌ Rejected

You may reapply after 7 days. Please ensure you meet all requirements before reapplying.

If you have questions, please contact server staff.
                    `)
                    .setThumbnail('https://cdn.discordapp.com/attachments/653195475201032202/1264287289517801493/logggopng.png')
                    .setFooter({ text: 'Whitelist System • Better luck next time' })
                    .setTimestamp();

                await user.send({ embeds: [rejectEmbed] });
            } catch (error) {
                console.error('Failed to send rejection DM:', error);
            }

            // Update review message
            await this.updateReviewMessage(interaction, applicationId, 'REJECTED', emojis.getError ? emojis.getError() : '❌');
        }

        // Send response
        try {
            if (interaction.deferred) {
                await interaction.editReply({
                    content: `${emojis.getSuccess ? emojis.getSuccess() : '✅'} Application ${decision}ed successfully. User has been notified.`
                });
            } else if (!interaction.replied) {
                await interaction.reply({
                    content: `${emojis.getSuccess ? emojis.getSuccess() : '✅'} Application ${decision}ed successfully. User has been notified.`,
                    ephemeral: true
                });
            }
        } catch (error) {
            console.log('Could not send response, interaction already handled');
        }
    }

    // Create private channel for accepted user
    async createPrivateChannel(guild, user, applicationId) {
        const channelName = `whitelist-${user.username.toLowerCase().replace(/[^a-z0-9]/g, '')}`;

        // Find admin and moderator roles
        const adminRoles = guild.roles.cache.filter(role =>
            role.name.toLowerCase().includes('admin') ||
            role.name.toLowerCase().includes('owner') ||
            role.permissions.has(PermissionFlagsBits.Administrator)
        );

        const modRoles = guild.roles.cache.filter(role =>
            role.name.toLowerCase().includes('mod') ||
            role.name.toLowerCase().includes('staff') ||
            role.permissions.has(PermissionFlagsBits.ManageMessages)
        );

        // Create permission overwrites array
        const permissionOverwrites = [
            {
                id: guild.roles.everyone.id,
                deny: [PermissionFlagsBits.ViewChannel]
            },
            {
                id: user.id,
                allow: [
                    PermissionFlagsBits.ViewChannel,
                    PermissionFlagsBits.SendMessages,
                    PermissionFlagsBits.ReadMessageHistory,
                    PermissionFlagsBits.AttachFiles,
                    PermissionFlagsBits.EmbedLinks
                ]
            },
            {
                id: guild.members.me.id,
                allow: [
                    PermissionFlagsBits.ViewChannel,
                    PermissionFlagsBits.SendMessages,
                    PermissionFlagsBits.ManageChannels,
                    PermissionFlagsBits.ReadMessageHistory
                ]
            }
        ];

        // Add admin roles permissions - with validation
        for (const role of adminRoles.values()) {
            if (role && role.id && guild.roles.cache.has(role.id)) {
                try {
                    permissionOverwrites.push({
                        id: role.id,
                        allow: [
                            PermissionFlagsBits.ViewChannel,
                            PermissionFlagsBits.SendMessages,
                            PermissionFlagsBits.ReadMessageHistory,
                            PermissionFlagsBits.AttachFiles,
                            PermissionFlagsBits.EmbedLinks,
                            PermissionFlagsBits.ManageMessages
                        ]
                    });
                } catch (error) {
                    console.error(`Failed to add admin role permissions for ${role.name}:`, error);
                }
            }
        }

        // Add moderator roles permissions - with validation
        for (const role of modRoles.values()) {
            if (role && role.id && guild.roles.cache.has(role.id)) {
                try {
                    permissionOverwrites.push({
                        id: role.id,
                        allow: [
                            PermissionFlagsBits.ViewChannel,
                            PermissionFlagsBits.SendMessages,
                            PermissionFlagsBits.ReadMessageHistory,
                            PermissionFlagsBits.AttachFiles,
                            PermissionFlagsBits.EmbedLinks
                        ]
                    });
                } catch (error) {
                    console.error(`Failed to add moderator role permissions for ${role.name}:`, error);
                }
            }
        }

        // Create private channel with error handling
        let privateChannel;
        try {
            privateChannel = await guild.channels.create({
                name: channelName,
                type: ChannelType.GuildText,
                permissionOverwrites: permissionOverwrites
            });
        } catch (error) {
            console.error('Failed to create private channel with permissions:', error);

            // Try creating with minimal permissions if the full permission set fails
            try {
                privateChannel = await guild.channels.create({
                    name: channelName,
                    type: ChannelType.GuildText,
                    permissionOverwrites: [
                        {
                            id: guild.roles.everyone.id,
                            deny: [PermissionFlagsBits.ViewChannel]
                        },
                        {
                            id: user.id,
                            allow: [
                                PermissionFlagsBits.ViewChannel,
                                PermissionFlagsBits.SendMessages,
                                PermissionFlagsBits.ReadMessageHistory
                            ]
                        },
                        {
                            id: guild.members.me.id,
                            allow: [
                                PermissionFlagsBits.ViewChannel,
                                PermissionFlagsBits.SendMessages,
                                PermissionFlagsBits.ManageChannels
                            ]
                        }
                    ]
                });
                console.log('✅ Created private channel with minimal permissions');
            } catch (fallbackError) {
                console.error('Failed to create private channel even with minimal permissions:', fallbackError);
                throw fallbackError;
            }
        }

        // Send welcome message
        const emojis = global.emojiReplacer || {};

        // Staff notification is handled in the embed, no separate string needed

        const welcomeEmbed = new EmbedBuilder()
            .setColor(0x00FF00)
            .setTitle(`${emojis.getSuccess ? emojis.getSuccess() : '🎉'} Welcome to the Whitelist!`)
            .setDescription(`
**Congratulations ${user}!**

Your whitelist application has been **APPROVED**! This private support channel has been created specifically for you.

${emojis.getGear ? emojis.getGear() : '⚙️'} **What you can do here:**
• Ask questions about server rules and gameplay
• Get help with game setup and configuration
• Request assistance with character creation
• Report any issues or problems
• Get guidance from our staff team

${emojis.getAdmin ? emojis.getAdmin() : '👥'} **Staff Support:**
Our admins and moderators are here to help you get started and ensure you have the best experience possible.

${emojis.getTime ? emojis.getTime() : '⏰'} **Channel Duration:**
This channel will automatically close in **24 hours** or you can close it manually when you're ready.

**Welcome to the community!** ${emojis.getSuccess ? emojis.getSuccess() : '🎉'}

${emojis.getInfo ? emojis.getInfo() : 'ℹ️'} **Getting Started:**
Feel free to ask any questions about:
• Server rules and guidelines
• Game installation and setup
• Character creation process
• Available roleplay scenarios

${emojis.getAdmin ? emojis.getAdmin() : '👥'} **Staff Notified:** Our staff team has been notified and will assist you shortly!
            `)
            .setThumbnail(user.displayAvatarURL())
            .setFooter({ text: 'Private Whitelist Support Channel • Auto-closes in 24 hours' })
            .setTimestamp();

        const actionButtons = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('close_private_channel')
                    .setLabel('Close Channel')
                    .setEmoji(emojis.getError ? emojis.getError() : '🔒')
                    .setStyle(ButtonStyle.Danger),
                new ButtonBuilder()
                    .setCustomId('extend_channel_time')
                    .setLabel('Extend Time')
                    .setEmoji(emojis.getTime ? emojis.getTime() : '⏰')
                    .setStyle(ButtonStyle.Secondary)
            );

        await privateChannel.send({
            content: `${user}`,
            embeds: [welcomeEmbed],
            components: [actionButtons]
        });

        // Staff notification is now included in the welcome embed above
        // No separate message needed to keep channel clean

        // Send additional helpful information
        const helpEmbed = new EmbedBuilder()
            .setColor(0x0099FF)
            .setTitle(`${emojis.getInfo ? emojis.getInfo() : 'ℹ️'} Quick Start Guide`)
            .setDescription('Here are some common questions and helpful resources:')
            .addFields(
                { name: `${emojis.getGear ? emojis.getGear() : '🔧'} Game Setup`, value: '• Download and install the game\n• Configure your settings\n• Test your connection', inline: true },
                { name: `${emojis.getUser ? emojis.getUser() : '👤'} Character Creation`, value: '• Choose your character name\n• Select appearance\n• Write your backstory', inline: true },
                { name: `${emojis.getServer ? emojis.getServer() : '🏠'} Server Rules`, value: '• Read the rules channel\n• Understand roleplay guidelines\n• Follow community standards', inline: true }
            )
            .setFooter({ text: 'Staff will help you with any of these topics!' });

        await privateChannel.send({ embeds: [helpEmbed] });

        // Auto-close after 24 hours
        setTimeout(async () => {
            try {
                // Send warning 1 hour before closing
                const warningEmbed = new EmbedBuilder()
                    .setColor(0xFFAA00)
                    .setTitle(`${emojis.getWarning ? emojis.getWarning() : '⚠️'} Channel Closing Soon`)
                    .setDescription('This private channel will close in 1 hour. If you need more time, please contact staff.')
                    .setTimestamp();

                await privateChannel.send({ embeds: [warningEmbed] });

                // Close after 1 more hour (total 24 hours)
                setTimeout(async () => {
                    try {
                        await privateChannel.delete('Auto-close after 24 hours');
                    } catch (error) {
                        console.error('Failed to auto-close private channel:', error);
                    }
                }, 60 * 60 * 1000); // 1 hour warning

            } catch (error) {
                console.error('Failed to send closing warning:', error);
            }
        }, this.config.autoCloseTime - (60 * 60 * 1000)); // 23 hours (24 - 1 hour warning)

        return privateChannel;
    }

    // Update review message with decision
    async updateReviewMessage(interaction, applicationId, status, emoji) {
        const embed = EmbedBuilder.from(interaction.message.embeds[0])
            .setColor(status === 'ACCEPTED' ? 0x00FF00 : 0xFF0000)
            .addFields(
                {
                    name: `${emoji} Status`,
                    value: `**${status}** by ${interaction.user.tag}\n<t:${Math.floor(Date.now() / 1000)}:R>`,
                    inline: false
                }
            );

        await interaction.message.edit({
            embeds: [embed],
            components: [] // Remove buttons
        });
    }
}

module.exports = AdvancedTicketSystem;
