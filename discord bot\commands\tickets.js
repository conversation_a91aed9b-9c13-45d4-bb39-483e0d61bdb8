const { <PERSON>lash<PERSON><PERSON>mandBuilder, Embed<PERSON>uilder, PermissionFlagsBits } = require('discord.js');
const TicketSystem = require('../utils/ticketSystem');
const AdvancedTicketSystem = require('../utils/advancedTicketSystem');
const Database = require('../database/database');

module.exports = {
    data: {
        name: 'ticket',
        description: 'Ticket system commands'
    },
    slashData: new SlashCommandBuilder()
        .setName('ticket')
        .setDescription('Ticket system commands')
        .setDMPermission(false)
        .addSubcommand(subcommand =>
            subcommand
                .setName('setup')
                .setDescription('Setup the ticket panel in a channel')
                .addChannelOption(option =>
                    option
                        .setName('channel')
                        .setDescription('Channel to send the ticket panel')
                        .setRequired(false)
                )
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('stats')
                .setDescription('View ticket statistics')
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('close')
                .setDescription('Close the current ticket')
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('add')
                .setDescription('Add a user to the current ticket')
                .addUserOption(option =>
                    option
                        .setName('user')
                        .setDescription('User to add to the ticket')
                        .setRequired(true)
                )
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('remove')
                .setDescription('Remove a user from the current ticket')
                .addUserOption(option =>
                    option
                        .setName('user')
                        .setDescription('User to remove from the ticket')
                        .setRequired(true)
                )
        ),

    async execute(interaction, args) {
        // Handle both slash commands and prefix commands
        if (interaction.isChatInputCommand && interaction.isChatInputCommand()) {
            // Slash command
            const subcommand = interaction.options.getSubcommand();
            return await this.handleSlashCommand(interaction, subcommand);
        } else {
            // Prefix command (message)
            return await this.handlePrefixCommand(interaction, args);
        }
    },

    async handleSlashCommand(interaction, subcommand) {

        switch (subcommand) {
            case 'setup':
                await this.handleSetup(interaction);
                break;
            case 'stats':
                await this.handleStats(interaction);
                break;
            case 'close':
                await this.handleClose(interaction);
                break;
            case 'add':
                await this.handleAdd(interaction);
                break;
            case 'remove':
                await this.handleRemove(interaction);
                break;
        }
    },

    async handlePrefixCommand(message, args) {
        const subcommand = args[0]?.toLowerCase();

        if (subcommand === 'setup') {
            // Check permissions
            if (!message.member.permissions.has(PermissionFlagsBits.ManageChannels)) {
                return await message.reply('❌ You need the "Manage Channels" permission to use this command.');
            }

            try {
                const ticketPanel = TicketSystem.createTicketPanel();
                await message.channel.send(ticketPanel);
                await message.reply('✅ Ticket panel has been set up!');
            } catch (error) {
                console.error('Error setting up ticket panel:', error);
                await message.reply('❌ Failed to setup ticket panel. Please check my permissions.');
            }
        } else {
            const embed = new EmbedBuilder()
                .setColor(0x0099FF)
                .setTitle('🎫 Ticket Commands')
                .setDescription('Available ticket commands:')
                .addFields(
                    { name: '/ticket setup', value: 'Setup ticket panel in current channel', inline: false },
                    { name: '/ticket stats', value: 'View ticket statistics', inline: false },
                    { name: '/ticket close', value: 'Close current ticket', inline: false },
                    { name: '/ticket add @user', value: 'Add user to current ticket', inline: false },
                    { name: '/ticket remove @user', value: 'Remove user from current ticket', inline: false }
                );

            await message.reply({ embeds: [embed] });
        }
    },

    async handleSetup(interaction) {
        // Check permissions
        if (!interaction.member.permissions.has(PermissionFlagsBits.ManageChannels)) {
            const errorEmoji = global.emojiReplacer ? global.emojiReplacer.getError() : '❌';
            return await interaction.reply({
                content: `${errorEmoji} You need the "Manage Channels" permission to use this command.`,
                flags: 64 // EPHEMERAL
            });
        }

        const channel = interaction.options.getChannel('channel') || interaction.channel;

        try {
            // Create advanced ticket system instance
            const advancedTicketSystem = new AdvancedTicketSystem(interaction.client);
            const ticketPanel = advancedTicketSystem.createAdvancedTicketPanel();
            await channel.send(ticketPanel);

            const successEmoji = global.emojiReplacer ? global.emojiReplacer.getSuccess() : '✅';
            await interaction.reply({
                content: `${successEmoji} **Advanced Ticket System** has been set up in ${channel}!\n\n**Features:**\n• Whitelist Application Forms\n• Admin Review System\n• Automatic DM Notifications\n• Private Channels\n• Auto-close Timer`,
                flags: 64 // EPHEMERAL
            });
        } catch (error) {
            console.error('Error setting up ticket panel:', error);
            const errorEmoji = global.emojiReplacer ? global.emojiReplacer.getError() : '❌';
            await interaction.reply({
                content: `${errorEmoji} Failed to setup ticket panel. Please check my permissions.`,
                flags: 64 // EPHEMERAL
            });
        }
    },

    async handleStats(interaction) {
        try {
            const stats = await Database.getTicketStats(interaction.guild.id);

            if (!stats || stats.length === 0) {
                return await interaction.reply({
                    content: '📊 No ticket statistics available yet.',
                    flags: 64 // EPHEMERAL
                });
            }

            const totalTickets = stats.reduce((sum, stat) => sum + stat.type_count, 0);
            const openTickets = interaction.guild.channels.cache.filter(ch =>
                ch.name.startsWith('ticket-')
            ).size;

            const embed = new EmbedBuilder()
                .setColor(0x0099FF)
                .setTitle('🎫 Ticket Statistics')
                .addFields(
                    { name: '📊 Total Tickets', value: totalTickets.toString(), inline: true },
                    { name: '🟢 Currently Open', value: openTickets.toString(), inline: true },
                    { name: '🔴 Total Closed', value: (totalTickets - openTickets).toString(), inline: true }
                )
                .setTimestamp();

            // Add ticket type breakdown
            if (stats.length > 0) {
                const typeBreakdown = stats.map(stat =>
                    `${TicketSystem.getTicketTypeDisplay(stat.ticket_type)}: ${stat.type_count}`
                ).join('\n');

                embed.addFields({
                    name: '📋 Ticket Types',
                    value: typeBreakdown || 'No data',
                    inline: false
                });
            }

            await interaction.reply({ embeds: [embed], flags: 64 }); // EPHEMERAL
        } catch (error) {
            console.error('Error getting ticket stats:', error);
            await interaction.reply({
                content: '❌ Failed to get ticket statistics.',
                flags: 64 // EPHEMERAL
            });
        }
    },

    async handleClose(interaction) {
        const channel = interaction.channel;

        if (!channel.name.startsWith('ticket-')) {
            return await interaction.reply({
                content: '❌ This command can only be used in ticket channels.',
                flags: 64 // EPHEMERAL
            });
        }

        try {
            await TicketSystem.handleCloseTicket(interaction);
        } catch (error) {
            console.error('Error closing ticket:', error);
            await interaction.reply({
                content: '❌ Failed to close ticket.',
                flags: 64 // EPHEMERAL
            });
        }
    },

    async handleAdd(interaction) {
        const channel = interaction.channel;
        const user = interaction.options.getUser('user');

        if (!channel.name.startsWith('ticket-')) {
            return await interaction.reply({
                content: '❌ This command can only be used in ticket channels.',
                flags: 64 // EPHEMERAL
            });
        }

        try {
            await channel.permissionOverwrites.create(user, {
                ViewChannel: true,
                SendMessages: true,
                ReadMessageHistory: true
            });

            await interaction.reply({
                content: `✅ Added ${user} to this ticket.`
            });
        } catch (error) {
            console.error('Error adding user to ticket:', error);
            await interaction.reply({
                content: '❌ Failed to add user to ticket.',
                flags: 64 // EPHEMERAL
            });
        }
    },

    async handleRemove(interaction) {
        const channel = interaction.channel;
        const user = interaction.options.getUser('user');

        if (!channel.name.startsWith('ticket-')) {
            return await interaction.reply({
                content: '❌ This command can only be used in ticket channels.',
                flags: 64 // EPHEMERAL
            });
        }

        try {
            await channel.permissionOverwrites.delete(user);

            await interaction.reply({
                content: `✅ Removed ${user} from this ticket.`
            });
        } catch (error) {
            console.error('Error removing user from ticket:', error);
            await interaction.reply({
                content: '❌ Failed to remove user from ticket.',
                flags: 64 // EPHEMERAL
            });
        }
    }
};
