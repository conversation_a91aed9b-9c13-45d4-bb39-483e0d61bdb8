const { <PERSON><PERSON><PERSON><PERSON><PERSON>Builder, Embed<PERSON>uilder, PermissionFlagsBits, ChannelType } = require('discord.js');

module.exports = {
    data: {
        name: 'advmod',
        description: 'Advanced moderation commands'
    },
    slashData: new SlashCommandBuilder()
        .setName('advmod')
        .setDescription('Advanced moderation commands')
        .setDefaultMemberPermissions(PermissionFlagsBits.ModerateMembers)
        .setDMPermission(false)
        .addSubcommand(subcommand =>
            subcommand
                .setName('slowmode')
                .setDescription('Set channel slowmode')
                .addIntegerOption(option =>
                    option.setName('seconds')
                        .setDescription('Slowmode duration in seconds (0-21600, 0 to disable)')
                        .setRequired(true)
                        .setMinValue(0)
                        .setMaxValue(21600))
                .addChannelOption(option =>
                    option.setName('channel')
                        .setDescription('Channel to set slowmode (default: current channel)')
                        .setRequired(false)))

        .addSubcommand(subcommand =>
            subcommand
                .setName('nuke')
                .setDescription('Delete and recreate a channel (clears all messages)')
                .addChannelOption(option =>
                    option.setName('channel')
                        .setDescription('Channel to nuke (default: current channel)')
                        .setRequired(false))
                .addStringOption(option =>
                    option.setName('reason')
                        .setDescription('Reason for nuking the channel')
                        .setRequired(false)))
        .addSubcommand(subcommand =>
            subcommand
                .setName('tempban')
                .setDescription('Temporarily ban a user')
                .addUserOption(option =>
                    option.setName('user')
                        .setDescription('User to temporarily ban')
                        .setRequired(true))
                .addStringOption(option =>
                    option.setName('duration')
                        .setDescription('Ban duration (e.g., 1h, 1d, 1w)')
                        .setRequired(true))
                .addStringOption(option =>
                    option.setName('reason')
                        .setDescription('Reason for the temporary ban')
                        .setRequired(false)))
        .addSubcommand(subcommand =>
            subcommand
                .setName('massban')
                .setDescription('Ban multiple users by ID')
                .addStringOption(option =>
                    option.setName('user_ids')
                        .setDescription('User IDs separated by spaces or commas')
                        .setRequired(true))
                .addStringOption(option =>
                    option.setName('reason')
                        .setDescription('Reason for the mass ban')
                        .setRequired(false))),

    async execute(interaction) {
        // Check permissions
        if (!interaction.member.permissions.has(PermissionFlagsBits.ModerateMembers)) {
            const errorEmoji = global.emojiReplacer ? global.emojiReplacer.getError() : '❌';
            return await interaction.reply({
                content: `${errorEmoji} You need **Moderate Members** permission to use this command.`,
                ephemeral: true
            });
        }

        const subcommand = interaction.options.getSubcommand();
        const emojis = global.emojiReplacer || {};

        try {
            switch (subcommand) {
                case 'slowmode':
                    await this.handleSlowmode(interaction, emojis);
                    break;

                case 'nuke':
                    await this.handleNuke(interaction, emojis);
                    break;
                case 'tempban':
                    await this.handleTempban(interaction, emojis);
                    break;
                case 'massban':
                    await this.handleMassban(interaction, emojis);
                    break;
                default:
                    await interaction.reply({
                        content: `${emojis.getError ? emojis.getError() : '❌'} Unknown subcommand.`,
                        ephemeral: true
                    });
            }
        } catch (error) {
            console.error('Advanced moderation command error:', error);
            
            if (!interaction.replied && !interaction.deferred) {
                await interaction.reply({
                    content: `${emojis.getError ? emojis.getError() : '❌'} An error occurred while executing the command.`,
                    ephemeral: true
                });
            }
        }
    },

    async handleSlowmode(interaction, emojis) {
        await interaction.deferReply();

        const seconds = interaction.options.getInteger('seconds');
        const channel = interaction.options.getChannel('channel') || interaction.channel;

        // Check if channel is text-based
        if (!channel.isTextBased()) {
            return await interaction.editReply({
                content: `${emojis.getError ? emojis.getError() : '❌'} Slowmode can only be set on text channels.`
            });
        }

        // Check permissions
        if (!channel.permissionsFor(interaction.guild.members.me).has(PermissionFlagsBits.ManageChannels)) {
            return await interaction.editReply({
                content: `${emojis.getError ? emojis.getError() : '❌'} I don't have permission to manage ${channel}.`
            });
        }

        try {
            await channel.setRateLimitPerUser(seconds, `Slowmode set by ${interaction.user.tag}`);

            const embed = new EmbedBuilder()
                .setColor(seconds > 0 ? 0xFFA500 : 0x00FF00)
                .setTitle(`${emojis.getGear ? emojis.getGear() : '⚙️'} Slowmode ${seconds > 0 ? 'Enabled' : 'Disabled'}`)
                .setDescription(seconds > 0 
                    ? `Slowmode set to **${seconds} seconds** in ${channel}`
                    : `Slowmode disabled in ${channel}`)
                .addFields({
                    name: `${emojis.getUser ? emojis.getUser() : '👤'} Moderator`,
                    value: interaction.user.toString(),
                    inline: true
                })
                .setTimestamp();

            await interaction.editReply({ embeds: [embed] });

            // Log activity
            if (global.logActivity) {
                global.logActivity('⚙️ SLOWMODE', `${seconds > 0 ? 'Set to ' + seconds + 's' : 'Disabled'} in #${channel.name}`, interaction.user, interaction.guild);
            }

        } catch (error) {
            console.error('Failed to set slowmode:', error);
            await interaction.editReply({
                content: `${emojis.getError ? emojis.getError() : '❌'} Failed to set slowmode. Please check my permissions.`
            });
        }
    },



    async handleNuke(interaction, emojis) {
        await interaction.deferReply({ ephemeral: true });

        const channel = interaction.options.getChannel('channel') || interaction.channel;
        const reason = interaction.options.getString('reason') || 'No reason provided';

        // Check if channel is text-based
        if (!channel.isTextBased()) {
            return await interaction.editReply({
                content: `${emojis.getError ? emojis.getError() : '❌'} Only text channels can be nuked.`
            });
        }

        // Check permissions
        if (!interaction.member.permissions.has(PermissionFlagsBits.ManageChannels)) {
            return await interaction.editReply({
                content: `${emojis.getError ? emojis.getError() : '❌'} You need **Manage Channels** permission to nuke channels.`
            });
        }

        if (!channel.permissionsFor(interaction.guild.members.me).has(PermissionFlagsBits.ManageChannels)) {
            return await interaction.editReply({
                content: `${emojis.getError ? emojis.getError() : '❌'} I don't have permission to manage ${channel}.`
            });
        }

        try {
            // Store channel properties
            const channelData = {
                name: channel.name,
                type: channel.type,
                topic: channel.topic,
                nsfw: channel.nsfw,
                rateLimitPerUser: channel.rateLimitPerUser,
                parent: channel.parent,
                position: channel.position,
                permissionOverwrites: channel.permissionOverwrites.cache.map(overwrite => ({
                    id: overwrite.id,
                    type: overwrite.type,
                    allow: overwrite.allow.bitfield,
                    deny: overwrite.deny.bitfield
                }))
            };

            // Delete the channel
            await channel.delete(`Channel nuked by ${interaction.user.tag}: ${reason}`);

            // Create new channel with same properties
            const newChannel = await interaction.guild.channels.create({
                name: channelData.name,
                type: channelData.type,
                topic: channelData.topic,
                nsfw: channelData.nsfw,
                rateLimitPerUser: channelData.rateLimitPerUser,
                parent: channelData.parent,
                position: channelData.position,
                reason: `Channel nuked by ${interaction.user.tag}: ${reason}`
            });

            // Restore permission overwrites
            for (const overwrite of channelData.permissionOverwrites) {
                try {
                    await newChannel.permissionOverwrites.create(overwrite.id, {
                        allow: overwrite.allow,
                        deny: overwrite.deny
                    });
                } catch (error) {
                    console.error('Failed to restore permission overwrite:', error);
                }
            }

            // Send nuke confirmation
            const nukeEmbed = new EmbedBuilder()
                .setColor(0xFF6B35)
                .setTitle(`${emojis.getBoom ? emojis.getBoom() : '💥'} Channel Nuked`)
                .setDescription(`This channel has been nuked and recreated by ${interaction.user}.\n\n**Reason:** ${reason}`)
                .addFields({
                    name: `${emojis.getInfo ? emojis.getInfo() : 'ℹ️'} Info`,
                    value: 'All previous messages have been deleted.',
                    inline: false
                })
                .setTimestamp();

            await newChannel.send({ embeds: [nukeEmbed] });

            // Log activity
            if (global.logActivity) {
                global.logActivity('💥 NUKE', `Nuked and recreated #${channelData.name}: ${reason}`, interaction.user, interaction.guild);
            }

            // Reply to the original interaction (if still valid)
            try {
                await interaction.editReply({
                    content: `${emojis.getSuccess ? emojis.getSuccess() : '✅'} **Channel nuked successfully!**\n\nRecreated: ${newChannel}`
                });
            } catch (error) {
                // Interaction might have expired, that's okay
                console.log('Nuke interaction expired, but operation completed successfully');
            }

        } catch (error) {
            console.error('Failed to nuke channel:', error);
            try {
                await interaction.editReply({
                    content: `${emojis.getError ? emojis.getError() : '❌'} Failed to nuke channel. Please check my permissions.`
                });
            } catch (replyError) {
                console.error('Failed to send nuke error reply:', replyError);
            }
        }
    },

    // Helper function to parse duration
    parseDuration(duration) {
        const regex = /^(\d+)([smhdw])$/i;
        const match = duration.match(regex);

        if (!match) return null;

        const value = parseInt(match[1]);
        const unit = match[2].toLowerCase();

        const multipliers = {
            s: 1000,
            m: 60 * 1000,
            h: 60 * 60 * 1000,
            d: 24 * 60 * 60 * 1000,
            w: 7 * 24 * 60 * 60 * 1000
        };

        return value * multipliers[unit];
    },

    async handleTempban(interaction, emojis) {
        await interaction.deferReply();

        const user = interaction.options.getUser('user');
        const duration = interaction.options.getString('duration');
        const reason = interaction.options.getString('reason') || 'No reason provided';

        // Check permissions
        if (!interaction.member.permissions.has(PermissionFlagsBits.BanMembers)) {
            return await interaction.editReply({
                content: `${emojis.getError ? emojis.getError() : '❌'} You need **Ban Members** permission to use this command.`
            });
        }

        // Parse duration
        const durationMs = this.parseDuration(duration);
        if (!durationMs) {
            return await interaction.editReply({
                content: `${emojis.getError ? emojis.getError() : '❌'} Invalid duration format. Use: 1s, 5m, 2h, 1d, 1w`
            });
        }

        // Check if user is bannable
        const member = interaction.guild.members.cache.get(user.id);
        if (member) {
            if (!member.bannable) {
                return await interaction.editReply({
                    content: `${emojis.getError ? emojis.getError() : '❌'} I cannot ban ${user.tag}. They may have higher permissions than me.`
                });
            }

            if (member.roles.highest.position >= interaction.member.roles.highest.position) {
                return await interaction.editReply({
                    content: `${emojis.getError ? emojis.getError() : '❌'} You cannot ban ${user.tag} as they have equal or higher role than you.`
                });
            }
        }

        try {
            // Ban the user
            await interaction.guild.members.ban(user, {
                reason: `Temporary ban by ${interaction.user.tag}: ${reason} (Duration: ${duration})`
            });

            // Schedule unban
            setTimeout(async () => {
                try {
                    await interaction.guild.members.unban(user.id, `Temporary ban expired (Duration: ${duration})`);
                    console.log(`⏰ TEMPBAN [${interaction.guild.name}] Auto-unbanned ${user.tag} after ${duration}`);
                } catch (error) {
                    console.error(`Failed to auto-unban ${user.tag}:`, error);
                }
            }, durationMs);

            const embed = new EmbedBuilder()
                .setColor(0xFF0000)
                .setTitle(`${emojis.getBan ? emojis.getBan() : '🔨'} Temporary Ban`)
                .setDescription(`${user.tag} has been temporarily banned.`)
                .addFields(
                    {
                        name: `${emojis.getUser ? emojis.getUser() : '👤'} User`,
                        value: `${user.tag} (${user.id})`,
                        inline: true
                    },
                    {
                        name: `${emojis.getClock ? emojis.getClock() : '⏰'} Duration`,
                        value: duration,
                        inline: true
                    },
                    {
                        name: `${emojis.getUser ? emojis.getUser() : '👤'} Moderator`,
                        value: interaction.user.toString(),
                        inline: true
                    },
                    {
                        name: `${emojis.getReason ? emojis.getReason() : '📝'} Reason`,
                        value: reason,
                        inline: false
                    }
                )
                .setTimestamp();

            await interaction.editReply({ embeds: [embed] });

            // Log activity
            if (global.logActivity) {
                global.logActivity('🔨 TEMPBAN', `${user.tag} for ${duration}: ${reason}`, interaction.user, interaction.guild);
            }

        } catch (error) {
            console.error('Failed to tempban user:', error);
            await interaction.editReply({
                content: `${emojis.getError ? emojis.getError() : '❌'} Failed to ban ${user.tag}. Please check my permissions.`
            });
        }
    },

    async handleMassban(interaction, emojis) {
        await interaction.deferReply({ ephemeral: true });

        const userIdsInput = interaction.options.getString('user_ids');
        const reason = interaction.options.getString('reason') || 'Mass ban';

        // Check permissions
        if (!interaction.member.permissions.has(PermissionFlagsBits.BanMembers)) {
            return await interaction.editReply({
                content: `${emojis.getError ? emojis.getError() : '❌'} You need **Ban Members** permission to use this command.`
            });
        }

        // Parse user IDs
        const userIds = userIdsInput.split(/[,\s]+/).filter(id => id.trim().length > 0);

        if (userIds.length === 0) {
            return await interaction.editReply({
                content: `${emojis.getError ? emojis.getError() : '❌'} No valid user IDs provided.`
            });
        }

        if (userIds.length > 10) {
            return await interaction.editReply({
                content: `${emojis.getError ? emojis.getError() : '❌'} Maximum 10 users can be banned at once.`
            });
        }

        let successful = 0;
        let failed = 0;
        const results = [];

        for (const userId of userIds) {
            try {
                // Validate user ID
                if (!/^\d{17,19}$/.test(userId)) {
                    results.push(`❌ Invalid ID: ${userId}`);
                    failed++;
                    continue;
                }

                // Check if user is in guild
                const member = interaction.guild.members.cache.get(userId);
                if (member) {
                    if (!member.bannable) {
                        results.push(`❌ Cannot ban: ${member.user.tag} (${userId})`);
                        failed++;
                        continue;
                    }

                    if (member.roles.highest.position >= interaction.member.roles.highest.position) {
                        results.push(`❌ Higher role: ${member.user.tag} (${userId})`);
                        failed++;
                        continue;
                    }
                }

                // Ban the user
                await interaction.guild.members.ban(userId, {
                    reason: `Mass ban by ${interaction.user.tag}: ${reason}`
                });

                const user = await interaction.client.users.fetch(userId).catch(() => null);
                const username = user ? user.tag : `Unknown (${userId})`;
                results.push(`✅ Banned: ${username}`);
                successful++;

            } catch (error) {
                console.error(`Failed to ban user ${userId}:`, error);
                results.push(`❌ Failed: ${userId}`);
                failed++;
            }
        }

        const embed = new EmbedBuilder()
            .setColor(successful > failed ? 0x00FF00 : 0xFF0000)
            .setTitle(`${emojis.getBan ? emojis.getBan() : '🔨'} Mass Ban Results`)
            .setDescription(`**Successful:** ${successful}\n**Failed:** ${failed}`)
            .addFields({
                name: `${emojis.getList ? emojis.getList() : '📋'} Results`,
                value: results.join('\n') || 'No results',
                inline: false
            })
            .addFields({
                name: `${emojis.getUser ? emojis.getUser() : '👤'} Moderator`,
                value: interaction.user.toString(),
                inline: true
            })
            .addFields({
                name: `${emojis.getReason ? emojis.getReason() : '📝'} Reason`,
                value: reason,
                inline: true
            })
            .setTimestamp();

        await interaction.editReply({ embeds: [embed] });

        // Log activity
        if (global.logActivity) {
            global.logActivity('🔨 MASSBAN', `${successful}/${userIds.length} users: ${reason}`, interaction.user, interaction.guild);
        }
    }
};
