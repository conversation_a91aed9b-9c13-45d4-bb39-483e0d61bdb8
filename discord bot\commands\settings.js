const { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>er, Embed<PERSON><PERSON><PERSON>, PermissionFlagsBits } = require('discord.js');
const Database = require('../database/database');

module.exports = {
    data: {
        name: 'settings',
        description: 'Configure bot settings',
    },
    slashData: new SlashCommandBuilder()
        .setName('settings')
        .setDescription('Configure bot settings')
        .setDefaultMemberPermissions(PermissionFlagsBits.ManageGuild)
        .setDMPermission(false)
        .addSubcommand(subcommand =>
            subcommand
                .setName('view')
                .setDescription('View current server settings'))
        .addSubcommand(subcommand =>
            subcommand
                .setName('prefix')
                .setDescription('Change the bot prefix')
                .addStringOption(option =>
                    option.setName('prefix')
                        .setDescription('New prefix (max 5 characters)')
                        .setRequired(true)
                        .setMaxLength(5)))
        .addSubcommandGroup(group =>
            group
                .setName('welcome')
                .setDescription('Configure welcome messages')
                .addSubcommand(subcommand =>
                    subcommand
                        .setName('channel')
                        .setDescription('Set welcome channel')
                        .addChannelOption(option =>
                            option.setName('channel')
                                .setDescription('Channel for welcome messages')
                                .setRequired(true)))
                .addSubcommand(subcommand =>
                    subcommand
                        .setName('message')
                        .setDescription('Set welcome message')
                        .addStringOption(option =>
                            option.setName('message')
                                .setDescription('Welcome message ({user} for mention, {server} for server name)')
                                .setRequired(true)))
                .addSubcommand(subcommand =>
                    subcommand
                        .setName('disable')
                        .setDescription('Disable welcome messages')))
        .addSubcommandGroup(group =>
            group
                .setName('logs')
                .setDescription('Configure logging')
                .addSubcommand(subcommand =>
                    subcommand
                        .setName('channel')
                        .setDescription('Set log channel')
                        .addChannelOption(option =>
                            option.setName('channel')
                                .setDescription('Channel for moderation logs')
                                .setRequired(true)))
                .addSubcommand(subcommand =>
                    subcommand
                        .setName('disable')
                        .setDescription('Disable logging')))
        .addSubcommand(subcommand =>
            subcommand
                .setName('automod')
                .setDescription('Toggle auto-moderation')
                .addBooleanOption(option =>
                    option.setName('enabled')
                        .setDescription('Enable or disable auto-moderation')
                        .setRequired(true))),
    aliases: ['config', 'setup'],
    cooldown: 5,
    async execute(interaction, args) {
        // Handle both slash commands and prefix commands
        if (interaction.isChatInputCommand && interaction.isChatInputCommand()) {
            // Slash command
            const subcommand = interaction.options.getSubcommand();
            const group = interaction.options.getSubcommandGroup(false);
            return await this.handleSlashCommand(interaction, group, subcommand);
        } else {
            // Prefix command (message)
            if (!interaction.member.permissions.has(PermissionFlagsBits.ManageGuild)) {
                return interaction.reply('❌ You need the "Manage Server" permission to use this command.');
            }

            const subcommand = args && args[0] ? args[0].toLowerCase() : 'view';

            switch (subcommand) {
                case 'prefix':
                    return await handlePrefix(interaction, args.slice(1));
                case 'welcome':
                    return await handleWelcome(interaction, args.slice(1));
                case 'logs':
                    return await handleLogs(interaction, args.slice(1));
                case 'automod':
                    return await handleAutomod(interaction, args.slice(1));
                case 'view':
                    return await handleView(interaction);
                default:
                    return await showSettingsHelp(interaction);
            }
        }
    },

    async handleSlashCommand(interaction, group, subcommand) {
        // Check permissions
        if (!interaction.member.permissions.has(PermissionFlagsBits.ManageGuild)) {
            return await interaction.reply({
                content: '❌ You need the "Manage Server" permission to use this command.',
                flags: 64
            });
        }

        try {
            await interaction.deferReply();

            if (group === 'welcome') {
                await this.handleSlashWelcome(interaction, subcommand);
            } else if (group === 'logs') {
                await this.handleSlashLogs(interaction, subcommand);
            } else {
                switch (subcommand) {
                    case 'view':
                        await this.handleSlashView(interaction);
                        break;
                    case 'prefix':
                        await this.handleSlashPrefix(interaction);
                        break;
                    case 'automod':
                        await this.handleSlashAutomod(interaction);
                        break;
                }
            }
        } catch (error) {
            console.error('Settings slash command error:', error);
            if (!interaction.replied && !interaction.deferred) {
                await interaction.reply({ content: '❌ Failed to update settings.', flags: 64 });
            } else {
                await interaction.editReply({ content: '❌ Failed to update settings.' });
            }
        }
    },

    async handleSlashView(interaction) {
        const settings = await Database.getGuildSettings(interaction.guild.id);

        const welcomeChannel = settings.welcome_channel ? `<#${settings.welcome_channel}>` : 'Not set';
        const logChannel = settings.log_channel ? `<#${settings.log_channel}>` : 'Not set';
        const automodStatus = settings.automod_enabled ? '✅ Enabled' : '❌ Disabled';

        const embed = new EmbedBuilder()
            .setColor(0x0099FF)
            .setTitle(`Settings for ${interaction.guild.name}`)
            .addFields(
                { name: 'Prefix', value: settings.prefix || '/', inline: true },
                { name: 'Welcome Channel', value: welcomeChannel, inline: true },
                { name: 'Log Channel', value: logChannel, inline: true },
                { name: 'Auto-Moderation', value: automodStatus, inline: true },
                { name: 'Level Up Messages', value: settings.level_up_messages ? '✅ Enabled' : '❌ Disabled', inline: true },
                { name: 'Welcome Message', value: settings.welcome_message || 'Default', inline: false }
            )
            .setTimestamp();

        await interaction.editReply({ embeds: [embed] });
    },

    async handleSlashWelcome(interaction, subcommand) {
        // Handle welcome-related slash commands
        await interaction.editReply({
            content: '⚠️ Welcome settings are currently being updated. Please use the legacy commands for now.',
            flags: 64
        });
    },

    async handleSlashLogs(interaction, subcommand) {
        // Handle logs-related slash commands
        await interaction.editReply({
            content: '⚠️ Log settings are currently being updated. Please use the legacy commands for now.',
            flags: 64
        });
    },

    async handleSlashPrefix(interaction) {
        // Handle prefix slash command
        await interaction.editReply({
            content: '⚠️ Prefix settings are currently being updated. Please use the legacy commands for now.',
            flags: 64
        });
    },

    async handleSlashAutomod(interaction) {
        // Handle automod slash command
        await interaction.editReply({
            content: '⚠️ Automod settings are currently being updated. Please use the legacy commands for now.',
            flags: 64
        });
    },
};

async function handlePrefix(message, args) {
    if (!args[0]) {
        return message.reply('❌ Please provide a new prefix. Example: `!settings prefix ?`');
    }

    const newPrefix = args[0];
    if (newPrefix.length > 5) {
        return message.reply('❌ Prefix must be 5 characters or less.');
    }

    try {
        await Database.updateGuildSetting(message.guild.id, 'prefix', newPrefix);

        const embed = new EmbedBuilder()
            .setColor(0x00FF00)
            .setTitle('Prefix Updated')
            .setDescription(`Bot prefix has been changed to: \`${newPrefix}\``)
            .setTimestamp();

        await message.reply({ embeds: [embed] });
    } catch (error) {
        console.error('Prefix update error:', error);
        await message.reply('❌ Failed to update prefix.');
    }
}

async function handleWelcome(message, args) {
    const action = args[0]?.toLowerCase();

    if (action === 'channel') {
        const channel = message.mentions.channels.first() || message.channel;

        try {
            await Database.updateGuildSetting(message.guild.id, 'welcome_channel', channel.id);

            const embed = new EmbedBuilder()
                .setColor(0x00FF00)
                .setTitle('Welcome Channel Set')
                .setDescription(`Welcome messages will now be sent to ${channel}`)
                .setTimestamp();

            await message.reply({ embeds: [embed] });
        } catch (error) {
            console.error('Welcome channel update error:', error);
            await message.reply('❌ Failed to set welcome channel.');
        }
    } else if (action === 'message') {
        const welcomeMessage = args.slice(1).join(' ');
        if (!welcomeMessage) {
            return message.reply('❌ Please provide a welcome message. Use `{user}` for user mention, `{server}` for server name.');
        }

        try {
            await Database.updateGuildSetting(message.guild.id, 'welcome_message', welcomeMessage);

            const embed = new EmbedBuilder()
                .setColor(0x00FF00)
                .setTitle('Welcome Message Updated')
                .setDescription(`Welcome message set to: ${welcomeMessage}`)
                .setTimestamp();

            await message.reply({ embeds: [embed] });
        } catch (error) {
            console.error('Welcome message update error:', error);
            await message.reply('❌ Failed to update welcome message.');
        }
    } else if (action === 'disable') {
        try {
            await Database.updateGuildSetting(message.guild.id, 'welcome_channel', null);

            const embed = new EmbedBuilder()
                .setColor(0xFF0000)
                .setTitle('Welcome Messages Disabled')
                .setDescription('Welcome messages have been disabled.')
                .setTimestamp();

            await message.reply({ embeds: [embed] });
        } catch (error) {
            console.error('Welcome disable error:', error);
            await message.reply('❌ Failed to disable welcome messages.');
        }
    } else {
        return message.reply('❌ Usage: `!settings welcome channel #channel` or `!settings welcome message <message>` or `!settings welcome disable`');
    }
}

async function handleLogs(message, args) {
    const action = args[0]?.toLowerCase();

    if (action === 'channel') {
        const channel = message.mentions.channels.first() || message.channel;

        try {
            await Database.updateGuildSetting(message.guild.id, 'log_channel', channel.id);

            const embed = new EmbedBuilder()
                .setColor(0x00FF00)
                .setTitle('Log Channel Set')
                .setDescription(`Moderation logs will now be sent to ${channel}`)
                .setTimestamp();

            await message.reply({ embeds: [embed] });
        } catch (error) {
            console.error('Log channel update error:', error);
            await message.reply('❌ Failed to set log channel.');
        }
    } else if (action === 'disable') {
        try {
            await Database.updateGuildSetting(message.guild.id, 'log_channel', null);

            const embed = new EmbedBuilder()
                .setColor(0xFF0000)
                .setTitle('Logging Disabled')
                .setDescription('Moderation logging has been disabled.')
                .setTimestamp();

            await message.reply({ embeds: [embed] });
        } catch (error) {
            console.error('Log disable error:', error);
            await message.reply('❌ Failed to disable logging.');
        }
    } else {
        return message.reply('❌ Usage: `!settings logs channel #channel` or `!settings logs disable`');
    }
}

async function handleAutomod(message, args) {
    const action = args[0]?.toLowerCase();

    if (action === 'enable') {
        try {
            await Database.updateGuildSetting(message.guild.id, 'automod_enabled', 1);

            const embed = new EmbedBuilder()
                .setColor(0x00FF00)
                .setTitle('Auto-Moderation Enabled')
                .setDescription('Auto-moderation features have been enabled.')
                .setTimestamp();

            await message.reply({ embeds: [embed] });
        } catch (error) {
            console.error('Automod enable error:', error);
            await message.reply('❌ Failed to enable auto-moderation.');
        }
    } else if (action === 'disable') {
        try {
            await Database.updateGuildSetting(message.guild.id, 'automod_enabled', 0);

            const embed = new EmbedBuilder()
                .setColor(0xFF0000)
                .setTitle('Auto-Moderation Disabled')
                .setDescription('Auto-moderation features have been disabled.')
                .setTimestamp();

            await message.reply({ embeds: [embed] });
        } catch (error) {
            console.error('Automod disable error:', error);
            await message.reply('❌ Failed to disable auto-moderation.');
        }
    } else {
        return message.reply('❌ Usage: `!settings automod enable` or `!settings automod disable`');
    }
}

async function handleView(message) {
    try {
        const settings = await Database.getGuildSettings(message.guild.id);

        const welcomeChannel = settings.welcome_channel ? `<#${settings.welcome_channel}>` : 'Not set';
        const logChannel = settings.log_channel ? `<#${settings.log_channel}>` : 'Not set';
        const automodStatus = settings.automod_enabled ? '✅ Enabled' : '❌ Disabled';

        const embed = new EmbedBuilder()
            .setColor(0x0099FF)
            .setTitle(`Settings for ${message.guild.name}`)
            .addFields(
                { name: 'Prefix', value: settings.prefix || '/', inline: true },
                { name: 'Welcome Channel', value: welcomeChannel, inline: true },
                { name: 'Log Channel', value: logChannel, inline: true },
                { name: 'Auto-Moderation', value: automodStatus, inline: true },
                { name: 'Level Up Messages', value: settings.level_up_messages ? '✅ Enabled' : '❌ Disabled', inline: true },
                { name: 'Welcome Message', value: settings.welcome_message || 'Default', inline: false }
            )
            .setTimestamp();

        await message.reply({ embeds: [embed] });
    } catch (error) {
        console.error('Settings view error:', error);
        await message.reply('❌ Failed to fetch settings.');
    }
}

async function showSettingsHelp(message) {
    const embed = new EmbedBuilder()
        .setColor(0x0099FF)
        .setTitle('Settings Commands')
        .setDescription('Configure bot settings for your server:')
        .addFields(
            { name: '/settings prefix <prefix>', value: 'Change the bot prefix', inline: false },
            { name: '/settings welcome channel #channel', value: 'Set welcome channel', inline: false },
            { name: '/settings welcome message <message>', value: 'Set welcome message', inline: false },
            { name: '/settings logs channel #channel', value: 'Set log channel', inline: false },
            { name: '/settings automod enable/disable', value: 'Toggle auto-moderation', inline: false },
            { name: '/settings view', value: 'View current settings', inline: false }
        )
        .setFooter({ text: 'Use {user} for user mention, {server} for server name in welcome messages' });

    await message.reply({ embeds: [embed] });
}
