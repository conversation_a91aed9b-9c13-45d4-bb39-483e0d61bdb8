const { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>er, Embed<PERSON><PERSON><PERSON>, PermissionFlagsBits } = require('discord.js');
const Database = require('../database/database');
const ms = require('ms');

module.exports = {
    data: {
        name: 'mod',
        description: 'Moderation commands',
    },
    slashData: new SlashCommandBuilder()
        .setName('mod')
        .setDescription('Moderation commands')
        .setDefaultMemberPermissions(PermissionFlagsBits.ModerateMembers)
        .setDMPermission(false)
        .addSubcommand(subcommand =>
            subcommand
                .setName('ban')
                .setDescription('Ban a user from the server')
                .addUserOption(option =>
                    option.setName('user')
                        .setDescription('User to ban')
                        .setRequired(true))
                .addStringOption(option =>
                    option.setName('reason')
                        .setDescription('Reason for the ban')
                        .setRequired(false)))
        .addSubcommand(subcommand =>
            subcommand
                .setName('kick')
                .setDescription('Kick a user from the server')
                .addUserOption(option =>
                    option.setName('user')
                        .setDescription('User to kick')
                        .setRequired(true))
                .addStringOption(option =>
                    option.setName('reason')
                        .setDescription('Reason for the kick')
                        .setRequired(false)))
        .addSubcommand(subcommand =>
            subcommand
                .setName('mute')
                .setDescription('Mute a user')
                .addUserOption(option =>
                    option.setName('user')
                        .setDescription('User to mute')
                        .setRequired(true))
                .addStringOption(option =>
                    option.setName('duration')
                        .setDescription('Duration (e.g., 10m, 1h, 1d)')
                        .setRequired(false))
                .addStringOption(option =>
                    option.setName('reason')
                        .setDescription('Reason for the mute')
                        .setRequired(false)))
        .addSubcommand(subcommand =>
            subcommand
                .setName('unmute')
                .setDescription('Unmute a user')
                .addUserOption(option =>
                    option.setName('user')
                        .setDescription('User to unmute')
                        .setRequired(true)))
        .addSubcommand(subcommand =>
            subcommand
                .setName('warn')
                .setDescription('Warn a user')
                .addUserOption(option =>
                    option.setName('user')
                        .setDescription('User to warn')
                        .setRequired(true))
                .addStringOption(option =>
                    option.setName('reason')
                        .setDescription('Reason for the warning')
                        .setRequired(false)))
        .addSubcommand(subcommand =>
            subcommand
                .setName('warnings')
                .setDescription('View warnings for a user')
                .addUserOption(option =>
                    option.setName('user')
                        .setDescription('User to check warnings for')
                        .setRequired(false)))
        .addSubcommand(subcommand =>
            subcommand
                .setName('purge')
                .setDescription('Delete multiple messages')
                .addIntegerOption(option =>
                    option.setName('amount')
                        .setDescription('Number of messages to delete (1-100)')
                        .setRequired(true)
                        .setMinValue(1)
                        .setMaxValue(100))),
    aliases: ['ban', 'kick', 'mute', 'warn', 'purge', 'warnings'],
    cooldown: 3,
    async execute(interaction, args) {
        // Handle both slash commands and prefix commands
        if (interaction.isChatInputCommand && interaction.isChatInputCommand()) {
            // Slash command
            const subcommand = interaction.options.getSubcommand();
            return await this.handleSlashCommand(interaction, subcommand);
        } else {
            // Prefix command (message)
            const subcommand = args[0]?.toLowerCase();
            switch (subcommand) {
                case 'ban':
                    return await handleBan(interaction, args.slice(1));
                case 'kick':
                    return await handleKick(interaction, args.slice(1));
                case 'mute':
                    return await handleMute(interaction, args.slice(1));
                case 'unmute':
                    return await handleUnmute(interaction, args.slice(1));
                case 'warn':
                    return await handleWarn(interaction, args.slice(1));
                case 'warnings':
                    return await handleWarnings(interaction, args.slice(1));
                case 'purge':
                    return await handlePurge(interaction, args.slice(1));
                default:
                    return await showModHelp(interaction);
            }
        }
    },

    async handleSlashCommand(interaction, subcommand) {
        switch (subcommand) {
            case 'ban':
                return await handleSlashBan(interaction);
            case 'kick':
                return await handleSlashKick(interaction);
            case 'mute':
                return await handleSlashMute(interaction);
            case 'unmute':
                return await handleSlashUnmute(interaction);
            case 'warn':
                return await handleSlashWarn(interaction);
            case 'warnings':
                return await handleSlashWarnings(interaction);
            case 'purge':
                return await handleSlashPurge(interaction);
        }
    },
};

async function handleBan(message, args) {
    if (!message.member.permissions.has(PermissionFlagsBits.BanMembers)) {
        return message.reply('❌ You need the "Ban Members" permission to use this command.');
    }

    const user = message.mentions.users.first() || await message.client.users.fetch(args[0]).catch(() => null);
    if (!user) {
        return message.reply('❌ Please mention a user or provide a valid user ID.');
    }

    const reason = args.slice(1).join(' ') || 'No reason provided';
    const member = message.guild.members.cache.get(user.id);

    if (member && !member.bannable) {
        return message.reply('❌ I cannot ban this user.');
    }

    try {
        await message.guild.members.ban(user, { reason: `${message.author.tag}: ${reason}` });

        const embed = new EmbedBuilder()
            .setColor(0xff0000) // Red for warning
            .setTitle('🚫 Member Banned')
            .addFields(
                { name: '👤 Banned User', value: `<@${user.id}> (${user.tag})`, inline: true },
                { name: '🆔 User ID', value: `${user.id}`, inline: true },
                { name: '🛡️ Banned By', value: `<@${message.author.id}>`, inline: true },
                { name: '📄 Reason', value: reason || 'No reason provided.', inline: false },
                { name: '🕒 Time', value: `<t:${Math.floor(Date.now() / 1000)}:F>`, inline: false }
            )
            .setTimestamp();

        await message.reply({ embeds: [embed] });
    } catch (error) {
        console.error('Ban error:', error);
        await message.reply('❌ Failed to ban the user.');
    }
}

async function handleKick(message, args) {
    if (!message.member.permissions.has(PermissionFlagsBits.KickMembers)) {
        return message.reply('❌ You need the "Kick Members" permission to use this command.');
    }

    const member = message.mentions.members.first();
    if (!member) {
        return message.reply('❌ Please mention a user to kick.');
    }

    if (!member.kickable) {
        return message.reply('❌ I cannot kick this user.');
    }

    const reason = args.slice(1).join(' ') || 'No reason provided';

    try {
        await member.kick(`${message.author.tag}: ${reason}`);

        const embed = new EmbedBuilder()
            .setColor(0xFFA500)
            .setTitle('User Kicked')
            .setDescription(`**User:** ${member.user.tag} (${member.id})\n**Reason:** ${reason}\n**Moderator:** ${message.author.tag}`)
            .setTimestamp();

        await message.reply({ embeds: [embed] });
    } catch (error) {
        console.error('Kick error:', error);
        await message.reply('❌ Failed to kick the user.');
    }
}

async function handleMute(message, args) {
    if (!message.member.permissions.has(PermissionFlagsBits.ModerateMembers)) {
        return message.reply('❌ You need the "Moderate Members" permission to use this command.');
    }

    const member = message.mentions.members.first();
    if (!member) {
        return message.reply('❌ Please mention a user to mute.');
    }

    if (!member.moderatable) {
        return message.reply('❌ I cannot mute this user.');
    }

    let duration = 10 * 60 * 1000; // Default 10 minutes
    let reason = 'No reason provided';

    // Parse duration and reason
    if (args[1]) {
        const parsedDuration = ms(args[1]);
        if (parsedDuration) {
            duration = parsedDuration;
            reason = args.slice(2).join(' ') || 'No reason provided';
        } else {
            // If duration parsing fails, treat as reason
            reason = args.slice(1).join(' ');
        }
    }

    if (!duration || duration > 28 * 24 * 60 * 60 * 1000) { // Max 28 days
        return message.reply('❌ Please provide a valid duration (max 28 days).');
    }

    try {
        await member.timeout(duration, `${message.author.tag}: ${reason}`);

        const embed = new EmbedBuilder()
            .setColor(0xFFFF00)
            .setTitle('User Muted')
            .setDescription(`**User:** ${member.user.tag} (${member.id})\n**Duration:** ${ms(duration, { long: true })}\n**Reason:** ${reason}\n**Moderator:** ${message.author.tag}`)
            .setTimestamp();

        await message.reply({ embeds: [embed] });
    } catch (error) {
        console.error('Mute error:', error);
        await message.reply('❌ Failed to mute the user.');
    }
}

async function handleUnmute(message, args) {
    if (!message.member.permissions.has(PermissionFlagsBits.ModerateMembers)) {
        return message.reply('❌ You need the "Moderate Members" permission to use this command.');
    }

    const member = message.mentions.members.first();
    if (!member) {
        return message.reply('❌ Please mention a user to unmute.');
    }

    try {
        await member.timeout(null, `Unmuted by ${message.author.tag}`);

        const embed = new EmbedBuilder()
            .setColor(0x00FF00)
            .setTitle('User Unmuted')
            .setDescription(`**User:** ${member.user.tag} (${member.id})\n**Moderator:** ${message.author.tag}`)
            .setTimestamp();

        await message.reply({ embeds: [embed] });
    } catch (error) {
        console.error('Unmute error:', error);
        await message.reply('❌ Failed to unmute the user.');
    }
}

async function handleWarn(message, args) {
    if (!message.member.permissions.has(PermissionFlagsBits.ModerateMembers)) {
        return message.reply('❌ You need the "Moderate Members" permission to use this command.');
    }

    const user = message.mentions.users.first();
    if (!user) {
        return message.reply('❌ Please mention a user to warn.');
    }

    const reason = args.slice(1).join(' ') || 'No reason provided';

    try {
        await Database.addWarning(user.id, message.guild.id, message.author.id, reason);

        const embed = new EmbedBuilder()
            .setColor(0xFFFF00)
            .setTitle('User Warned')
            .setDescription(`**User:** ${user.tag} (${user.id})\n**Reason:** ${reason}\n**Moderator:** ${message.author.tag}`)
            .setTimestamp();

        await message.reply({ embeds: [embed] });

        // Try to DM the user
        try {
            await user.send(`You have been warned in **${message.guild.name}** for: ${reason}`);
        } catch (error) {
            // User has DMs disabled
        }
    } catch (error) {
        console.error('Warning error:', error);
        await message.reply('❌ Failed to warn the user.');
    }
}

async function handleWarnings(message, args) {
    const user = message.mentions.users.first() || message.author;

    try {
        const warnings = await Database.getWarnings(user.id, message.guild.id);

        if (warnings.length === 0) {
            return message.reply(`${user.tag} has no warnings.`);
        }

        const embed = new EmbedBuilder()
            .setColor(0xFFFF00)
            .setTitle(`Warnings for ${user.tag}`)
            .setDescription(warnings.slice(0, 10).map((w, i) =>
                `**${i + 1}.** ${w.reason}\n*By <@${w.moderator_id}> on ${new Date(w.timestamp).toLocaleDateString()}*`
            ).join('\n\n'))
            .setFooter({ text: `Total warnings: ${warnings.length}` });

        await message.reply({ embeds: [embed] });
    } catch (error) {
        console.error('Warnings error:', error);
        await message.reply('❌ Failed to fetch warnings.');
    }
}

async function handlePurge(message, args) {
    if (!message.member.permissions.has(PermissionFlagsBits.ManageMessages)) {
        return message.reply('❌ You need the "Manage Messages" permission to use this command.');
    }

    const amount = parseInt(args[0]);
    if (!amount || amount < 1 || amount > 100) {
        return message.reply('❌ Please provide a number between 1 and 100.');
    }

    try {
        const deleted = await message.channel.bulkDelete(amount + 1, true);
        const reply = await message.channel.send(`✅ Deleted ${deleted.size - 1} messages.`);

        setTimeout(() => reply.delete().catch(() => {}), 5000);
    } catch (error) {
        console.error('Purge error:', error);
        await message.reply('❌ Failed to delete messages.');
    }
}

async function showModHelp(message) {
    const embed = new EmbedBuilder()
        .setColor(0x0099FF)
        .setTitle('Moderation Commands')
        .setDescription('Available moderation commands:')
        .addFields(
            { name: '/mod ban <user> [reason]', value: 'Ban a user', inline: true },
            { name: '/mod kick <user> [reason]', value: 'Kick a user', inline: true },
            { name: '/mod mute <user> [duration] [reason]', value: 'Mute a user', inline: true },
            { name: '/mod unmute <user>', value: 'Unmute a user', inline: true },
            { name: '/mod warn <user> [reason]', value: 'Warn a user', inline: true },
            { name: '/mod warnings [user]', value: 'View warnings', inline: true },
            { name: '/mod purge <amount>', value: 'Delete messages', inline: true }
        );

    await message.reply({ embeds: [embed] });
}

// Slash command handlers
async function handleSlashBan(interaction) {
    // Check if command is used in a guild
    if (!interaction.guild) {
        return interaction.reply({ content: '❌ This command can only be used in a server.', flags: 64 });
    }

    // Check if member exists and has permissions
    if (!interaction.member || !interaction.member.permissions || !interaction.member.permissions.has(PermissionFlagsBits.BanMembers)) {
        return interaction.reply({ content: '❌ You need the "Ban Members" permission to use this command.', flags: 64 });
    }

    try {
        const user = interaction.options.getUser('user');
        const reason = interaction.options.getString('reason') || 'No reason provided';
        const member = interaction.guild.members.cache.get(user.id);

        if (member && !member.bannable) {
            return await interaction.reply({ content: '❌ I cannot ban this user.', flags: 64 });
        }

        await interaction.guild.members.ban(user, { reason: `${interaction.user.tag}: ${reason}` });

        // Get custom emojis
        const emojis = global.emojiManager ? {
            icon37: global.emojiManager.get('icon37'),
            icon5: global.emojiManager.get('icon5'),
            adminmod: global.emojiManager.get('adminmod'),
            icon: global.emojiManager.get('icon'),
            icon3: global.emojiManager.get('icon3'),
            icon20: global.emojiManager.get('icon20'),
            icon7: global.emojiManager.get('icon7'),
            ban: global.emojiManager.get('ban'),
            hammer: global.emojiManager.get('hammer'),
            shield: global.emojiManager.get('shield')
        } : {
            icon37: '🚫',
            icon5: '👤',
            adminmod: '🛡️',
            icon: '📄',
            icon3: '⏰',
            icon20: '📅',
            icon7: '💬',
            ban: '🔨',
            hammer: '🔨',
            shield: '🛡️'
        };

        const embed = new EmbedBuilder()
            .setColor(15158332) // Red color
            .setTitle(`${emojis.icon37} User Banned`)
            .setThumbnail('https://cdn.discordapp.com/attachments/653195475201032202/1264287289517801493/logggopng.png')
            .addFields(
                { name: `${emojis.icon5} User`, value: `<@${user.id}> (\`${user.id}\`)`, inline: true },
                { name: `${emojis.adminmod} Banned By`, value: `<@${interaction.user.id}>`, inline: true },
                { name: `${emojis.icon} Reason`, value: reason || 'No reason provided', inline: false },
                { name: `${emojis.icon3} Duration`, value: 'Permanent', inline: true },
                { name: `${emojis.icon20} Date`, value: `<t:${Math.floor(Date.now() / 1000)}:R>`, inline: true },
                { name: `${emojis.icon7} Message`, value: 'User has been permanently banned from the server.', inline: false }
            )
            .setFooter({ text: 'Server Ban Logs' });

        await interaction.reply({ embeds: [embed] });
    } catch (error) {
        console.error('Ban error:', error);
        if (!interaction.replied && !interaction.deferred) {
            await interaction.reply({ content: '❌ Failed to ban the user.', flags: 64 });
        } else {
            await interaction.followUp({ content: '❌ Failed to ban the user.', flags: 64 });
        }
    }
}

async function handleSlashKick(interaction) {
    // Check if command is used in a guild
    if (!interaction.guild) {
        return interaction.reply({ content: '❌ This command can only be used in a server.', flags: 64 });
    }

    // Check if member exists and has permissions
    if (!interaction.member || !interaction.member.permissions || !interaction.member.permissions.has(PermissionFlagsBits.KickMembers)) {
        return interaction.reply({ content: '❌ You need the "Kick Members" permission to use this command.', flags: 64 });
    }

    const user = interaction.options.getUser('user');
    const member = interaction.guild.members.cache.get(user.id);
    const reason = interaction.options.getString('reason') || 'No reason provided';

    if (!member) {
        return interaction.reply({ content: '❌ User not found in this server.', flags: 64 });
    }

    if (!member.kickable) {
        return interaction.reply({ content: '❌ I cannot kick this user.', flags: 64 });
    }

    try {
        await member.kick(`${interaction.user.tag}: ${reason}`);

        const embed = new EmbedBuilder()
            .setColor(0xFFA500)
            .setTitle('User Kicked')
            .setDescription(`**User:** ${user.tag} (${user.id})\n**Reason:** ${reason}\n**Moderator:** ${interaction.user.tag}`)
            .setTimestamp();

        await interaction.reply({ embeds: [embed] });
    } catch (error) {
        console.error('Kick error:', error);
        await interaction.reply({ content: '❌ Failed to kick the user.', flags: 64 });
    }
}

async function handleSlashMute(interaction) {
    // Check if command is used in a guild
    if (!interaction.guild) {
        return interaction.reply({ content: '❌ This command can only be used in a server.', flags: 64 });
    }

    // Check if member exists and has permissions
    if (!interaction.member || !interaction.member.permissions || !interaction.member.permissions.has(PermissionFlagsBits.ModerateMembers)) {
        return interaction.reply({ content: '❌ You need the "Moderate Members" permission to use this command.', flags: 64 });
    }

    const user = interaction.options.getUser('user');
    const member = interaction.guild.members.cache.get(user.id);
    const durationStr = interaction.options.getString('duration') || '10m';
    const reason = interaction.options.getString('reason') || 'No reason provided';

    if (!member) {
        return interaction.reply({ content: '❌ User not found in this server.', flags: 64 });
    }

    if (!member.moderatable) {
        return interaction.reply({ content: '❌ I cannot mute this user.', flags: 64 });
    }

    const duration = ms(durationStr);
    if (!duration || duration > 28 * 24 * 60 * 60 * 1000) {
        return interaction.reply({ content: '❌ Please provide a valid duration (max 28 days).', flags: 64 });
    }

    try {
        await member.timeout(duration, `${interaction.user.tag}: ${reason}`);

        const embed = new EmbedBuilder()
            .setColor(0xFFFF00)
            .setTitle('User Muted')
            .setDescription(`**User:** ${user.tag} (${user.id})\n**Duration:** ${ms(duration, { long: true })}\n**Reason:** ${reason}\n**Moderator:** ${interaction.user.tag}`)
            .setTimestamp();

        await interaction.reply({ embeds: [embed] });
    } catch (error) {
        console.error('Mute error:', error);
        await interaction.reply({ content: '❌ Failed to mute the user.', flags: 64 });
    }
}

async function handleSlashUnmute(interaction) {
    // Check if command is used in a guild
    if (!interaction.guild) {
        return interaction.reply({ content: '❌ This command can only be used in a server.', flags: 64 });
    }

    // Check if member exists and has permissions
    if (!interaction.member || !interaction.member.permissions || !interaction.member.permissions.has(PermissionFlagsBits.ModerateMembers)) {
        return interaction.reply({ content: '❌ You need the "Moderate Members" permission to use this command.', flags: 64 });
    }

    const user = interaction.options.getUser('user');
    const member = interaction.guild.members.cache.get(user.id);

    if (!member) {
        return interaction.reply({ content: '❌ User not found in this server.', flags: 64 });
    }

    try {
        await member.timeout(null, `Unmuted by ${interaction.user.tag}`);

        const embed = new EmbedBuilder()
            .setColor(0x00FF00)
            .setTitle('User Unmuted')
            .setDescription(`**User:** ${user.tag} (${user.id})\n**Moderator:** ${interaction.user.tag}`)
            .setTimestamp();

        await interaction.reply({ embeds: [embed] });
    } catch (error) {
        console.error('Unmute error:', error);
        await interaction.reply({ content: '❌ Failed to unmute the user.', flags: 64 });
    }
}

async function handleSlashWarn(interaction) {
    // Check if command is used in a guild
    if (!interaction.guild) {
        return interaction.reply({ content: '❌ This command can only be used in a server.', flags: 64 });
    }

    // Check if member exists and has permissions
    if (!interaction.member || !interaction.member.permissions || !interaction.member.permissions.has(PermissionFlagsBits.ModerateMembers)) {
        return interaction.reply({ content: '❌ You need the "Moderate Members" permission to use this command.', flags: 64 });
    }

    const user = interaction.options.getUser('user');
    const reason = interaction.options.getString('reason') || 'No reason provided';

    try {
        await Database.addWarning(user.id, interaction.guild.id, interaction.user.id, reason);

        const embed = new EmbedBuilder()
            .setColor(0xFFFF00)
            .setTitle('User Warned')
            .setDescription(`**User:** ${user.tag} (${user.id})\n**Reason:** ${reason}\n**Moderator:** ${interaction.user.tag}`)
            .setTimestamp();

        await interaction.reply({ embeds: [embed] });

        // Try to DM the user
        try {
            await user.send(`You have been warned in **${interaction.guild.name}** for: ${reason}`);
        } catch (error) {
            // User has DMs disabled
        }
    } catch (error) {
        console.error('Warning error:', error);
        await interaction.reply({ content: '❌ Failed to warn the user.', flags: 64 });
    }
}

async function handleSlashWarnings(interaction) {
    try {
        // Defer reply to prevent timeout
        await interaction.deferReply({ flags: 64 });

        const user = interaction.options.getUser('user') || interaction.user;
        const warnings = await Database.getWarnings(user.id, interaction.guild.id);

        if (warnings.length === 0) {
            return await interaction.editReply({ content: `${user.tag} has no warnings.` });
        }

        const embed = new EmbedBuilder()
            .setColor(0xFFFF00)
            .setTitle(`Warnings for ${user.tag}`)
            .setDescription(warnings.slice(0, 10).map((w, i) =>
                `**${i + 1}.** ${w.reason}\n*By <@${w.moderator_id}> on ${new Date(w.timestamp).toLocaleDateString()}*`
            ).join('\n\n'))
            .setFooter({ text: `Total warnings: ${warnings.length}` });

        await interaction.editReply({ embeds: [embed] });
    } catch (error) {
        console.error('Warnings error:', error);
        if (!interaction.replied && !interaction.deferred) {
            await interaction.reply({ content: '❌ Failed to fetch warnings.', flags: 64 });
        } else {
            await interaction.editReply({ content: '❌ Failed to fetch warnings.' });
        }
    }
}

async function handleSlashPurge(interaction) {
    // Check if command is used in a guild
    if (!interaction.guild) {
        return interaction.reply({ content: '❌ This command can only be used in a server.', flags: 64 });
    }

    // Check if member exists and has permissions
    if (!interaction.member || !interaction.member.permissions || !interaction.member.permissions.has(PermissionFlagsBits.ManageMessages)) {
        return interaction.reply({ content: '❌ You need the "Manage Messages" permission to use this command.', flags: 64 });
    }

    const amount = interaction.options.getInteger('amount');

    try {
        const deleted = await interaction.channel.bulkDelete(amount, true);
        await interaction.reply({ content: `✅ Deleted ${deleted.size} messages.`, flags: 64 });
    } catch (error) {
        console.error('Purge error:', error);
        await interaction.reply({ content: '❌ Failed to delete messages.', flags: 64 });
    }
}
