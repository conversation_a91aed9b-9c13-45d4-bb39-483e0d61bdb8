const { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON><PERSON>, PermissionFlagsBits } = require('discord.js');
const Database = require('../database/database');

module.exports = {
    data: {
        name: 'util',
        description: 'Utility commands',
    },
    slashData: new SlashCommandBuilder()
        .setName('util')
        .setDescription('Utility commands')
        .addSubcommand(subcommand =>
            subcommand
                .setName('userinfo')
                .setDescription('Get information about a user')
                .addUserOption(option =>
                    option.setName('user')
                        .setDescription('User to get info about')
                        .setRequired(false)))
        .addSubcommand(subcommand =>
            subcommand
                .setName('serverinfo')
                .setDescription('Get information about the server'))
        .addSubcommand(subcommand =>
            subcommand
                .setName('avatar')
                .setDescription('Get a user\'s avatar')
                .addUserOption(option =>
                    option.setName('user')
                        .setDescription('User to get avatar of')
                        .setRequired(false)))
        .addSubcommand(subcommand =>
            subcommand
                .setName('ping')
                .setDescription('Check bot latency'))
        .addSubcommand(subcommand =>
            subcommand
                .setName('help')
                .setDescription('Show help menu')
                .addStringOption(option =>
                    option.setName('category')
                        .setDescription('Choose a specific category to view')
                        .setRequired(false)
                        .addChoices(
                            { name: '🛡️ Admin Commands', value: 'admin' },
                            { name: '👥 Public Commands', value: 'public' },
                            { name: '🎫 Support System', value: 'support' },
                            { name: '🏆 Economy & Leveling', value: 'economy' },
                            { name: '🎁 Fun & Entertainment', value: 'fun' },
                            { name: '⚙️ Server Setup', value: 'setup' }
                        ))),
    aliases: ['userinfo', 'serverinfo', 'avatar', 'ping', 'help'],
    cooldown: 3,
    async execute(interaction, args) {
        // Handle both slash commands and prefix commands
        if (interaction.isChatInputCommand && interaction.isChatInputCommand()) {
            // Slash command
            const subcommand = interaction.options.getSubcommand();
            return await this.handleSlashCommand(interaction, subcommand);
        } else {
            // Prefix command (message)
            const subcommand = args[0]?.toLowerCase();
            switch (subcommand) {
                case 'userinfo':
                    return await handleUserInfo(interaction, args.slice(1));
                case 'serverinfo':
                    return await handleServerInfo(interaction);
                case 'avatar':
                    return await handleAvatar(interaction, args.slice(1));
                case 'ping':
                    return await handlePing(interaction);
                case 'help':
                    return await handleHelp(interaction);
                default:
                    return await handleHelp(interaction);
            }
        }
    },

    async handleSlashCommand(interaction, subcommand) {
        switch (subcommand) {
            case 'userinfo':
                return await handleSlashUserInfo(interaction);
            case 'serverinfo':
                return await handleSlashServerInfo(interaction);
            case 'avatar':
                return await handleSlashAvatar(interaction);
            case 'ping':
                return await handleSlashPing(interaction);
            case 'help':
                return await handleSlashHelp(interaction);
        }
    },
};

async function handleUserInfo(message, args) {
    const user = message.mentions.users.first() || message.author;
    const member = message.guild.members.cache.get(user.id);

    if (!member) {
        const errorEmoji = global.emojiManager ? global.emojiManager.get('error') : '❌';
        return message.reply(`${errorEmoji} User not found in this server.`);
    }

    try {
        const userData = await Database.getUserXP(user.id, message.guild.id);

        const embed = new EmbedBuilder()
            .setColor(0x0099FF)
            .setTitle(`User Info - ${user.tag}`)
            .setThumbnail(user.displayAvatarURL({ dynamic: true }))
            .addFields(
                { name: 'ID', value: user.id, inline: true },
                { name: 'Nickname', value: member.nickname || 'None', inline: true },
                { name: 'Account Created', value: `<t:${Math.floor(user.createdTimestamp / 1000)}:R>`, inline: true },
                { name: 'Joined Server', value: member.joinedAt ? `<t:${Math.floor(member.joinedAt.getTime() / 1000)}:R>` : 'Unknown', inline: true },
                { name: 'Level', value: userData.level.toString(), inline: true },
                { name: 'XP', value: userData.xp.toString(), inline: true },
                { name: 'Roles', value: member.roles.cache.filter(role => role.name !== '@everyone').map(role => role.toString()).join(', ') || 'None', inline: false }
            )
            .setFooter({ text: `Requested by ${message.author.tag}` })
            .setTimestamp();

        await message.reply({ embeds: [embed] });
    } catch (error) {
        console.error('UserInfo error:', error);
        const errorEmoji = global.emojiManager ? global.emojiManager.get('error') : '❌';
        await message.reply(`${errorEmoji} Failed to fetch user information.`);
    }
}

async function handleServerInfo(message) {
    const guild = message.guild;

    try {
        const owner = await guild.fetchOwner();

        const embed = new EmbedBuilder()
            .setColor(0x0099FF)
            .setTitle(`Server Info - ${guild.name}`)
            .setThumbnail(guild.iconURL({ dynamic: true }))
            .addFields(
                { name: 'ID', value: guild.id, inline: true },
                { name: 'Owner', value: owner.user.tag, inline: true },
                { name: 'Created', value: `<t:${Math.floor(guild.createdTimestamp / 1000)}:R>`, inline: true },
                { name: 'Members', value: guild.memberCount.toString(), inline: true },
                { name: 'Channels', value: guild.channels.cache.size.toString(), inline: true },
                { name: 'Roles', value: guild.roles.cache.size.toString(), inline: true },
                { name: 'Boost Level', value: guild.premiumTier.toString(), inline: true },
                { name: 'Boosts', value: guild.premiumSubscriptionCount?.toString() || '0', inline: true },
                { name: 'Verification Level', value: guild.verificationLevel.toString(), inline: true }
            )
            .setFooter({ text: `Requested by ${message.author.tag}` })
            .setTimestamp();

        await message.reply({ embeds: [embed] });
    } catch (error) {
        console.error('ServerInfo error:', error);
        await message.reply('❌ Failed to fetch server information.');
    }
}

async function handleAvatar(message, args) {
    const user = message.mentions.users.first() || message.author;

    const embed = new EmbedBuilder()
        .setColor(0x0099FF)
        .setTitle(`${user.tag}'s Avatar`)
        .setImage(user.displayAvatarURL({ dynamic: true, size: 512 }))
        .setFooter({ text: `Requested by ${message.author.tag}` });

    await message.reply({ embeds: [embed] });
}

async function handlePing(message) {
    const pingEmoji = global.emojiManager ? global.emojiManager.get('ping') : '🏓';
    const sent = await message.reply(`${pingEmoji} Pinging...`);
    const timeDiff = sent.createdTimestamp - message.createdTimestamp;

    const embed = new EmbedBuilder()
        .setColor(0x00FF00)
        .setTitle(`${pingEmoji} Pong!`)
        .addFields(
            { name: 'Bot Latency', value: `${timeDiff}ms`, inline: true },
            { name: 'API Latency', value: `${Math.round(message.client.ws.ping)}ms`, inline: true }
        );

    await sent.edit({ content: '', embeds: [embed] });
}

async function handleHelp(message) {
    const emojis = global.emojiReplacer || {};

    // Main help embed with beautiful design
    const mainEmbed = new EmbedBuilder()
        .setColor(0x00D4FF)
        .setTitle(`${emojis.getBot ? emojis.getBot() : '🤖'} **Shanta Somali Bot** - Command Center`)
        .setDescription(`${emojis.getWave ? emojis.getWave() : '👋'} **Welcome to the most advanced Discord bot!**\n\n${emojis.getInfo ? emojis.getInfo() : 'ℹ️'} **Choose a category below to explore commands:**`)
        .setThumbnail('https://cdn.discordapp.com/attachments/653195475201032202/1264287289517801493/logggopng.png')
        .addFields(
            {
                name: `${emojis.getShield ? emojis.getShield() : '🛡️'} **ADMINISTRATION**`,
                value: `${emojis.getArrow ? emojis.getArrow() : '▸'} Moderation & Server Management\n${emojis.getArrow ? emojis.getArrow() : '▸'} Anti-Spam & Security Systems\n${emojis.getArrow ? emojis.getArrow() : '▸'} Advanced Configuration Tools`,
                inline: true
            },
            {
                name: `${emojis.getUsers ? emojis.getUsers() : '👥'} **COMMUNITY**`,
                value: `${emojis.getArrow ? emojis.getArrow() : '▸'} Welcome & Goodbye Systems\n${emojis.getArrow ? emojis.getArrow() : '▸'} Role Management & Reactions\n${emojis.getArrow ? emojis.getArrow() : '▸'} Verification & Security`,
                inline: true
            },
            {
                name: `${emojis.getTicket ? emojis.getTicket() : '🎫'} **SUPPORT**`,
                value: `${emojis.getArrow ? emojis.getArrow() : '▸'} Advanced Ticket System\n${emojis.getArrow ? emojis.getArrow() : '▸'} Private Channels & Whitelist\n${emojis.getArrow ? emojis.getArrow() : '▸'} Staff Management Tools`,
                inline: true
            },
            {
                name: `${emojis.getTrophy ? emojis.getTrophy() : '🏆'} **ENGAGEMENT**`,
                value: `${emojis.getArrow ? emojis.getArrow() : '▸'} Leveling & XP System\n${emojis.getArrow ? emojis.getArrow() : '▸'} Economy & Rewards\n${emojis.getArrow ? emojis.getArrow() : '▸'} Leaderboards & Rankings`,
                inline: true
            },
            {
                name: `${emojis.getGift ? emojis.getGift() : '🎁'} **ENTERTAINMENT**`,
                value: `${emojis.getArrow ? emojis.getArrow() : '▸'} Fun Games & Activities\n${emojis.getArrow ? emojis.getArrow() : '▸'} Giveaways & Events\n${emojis.getArrow ? emojis.getArrow() : '▸'} Memes & Random Content`,
                inline: true
            },
            {
                name: `${emojis.getGear ? emojis.getGear() : '⚙️'} **UTILITIES**`,
                value: `${emojis.getArrow ? emojis.getArrow() : '▸'} Server & User Information\n${emojis.getArrow ? emojis.getArrow() : '▸'} Bot Status & Performance\n${emojis.getArrow ? emojis.getArrow() : '▸'} Logging & Analytics`,
                inline: true
            }
        )
        .addFields({
            name: `${emojis.getStar ? emojis.getStar() : '⭐'} **Quick Commands**`,
            value: `${emojis.getDot ? emojis.getDot() : '•'} \`/util help category:admin\` - Admin commands\n${emojis.getDot ? emojis.getDot() : '•'} \`/util help category:public\` - Public commands\n${emojis.getDot ? emojis.getDot() : '•'} \`/setup dashboard\` - Server setup\n${emojis.getDot ? emojis.getDot() : '•'} \`/util ping\` - Bot status`,
            inline: false
        })
        .setFooter({
            text: `${emojis.getPowered ? emojis.getPowered() : '⚡'} Powered by Shanta Somali • ${emojis.getOnline ? emojis.getOnline() : '🟢'} Online & Ready`,
            iconURL: 'https://cdn.discordapp.com/attachments/653195475201032202/1264287289517801493/logggopng.png'
        })
        .setTimestamp();

    await message.reply({ embeds: [mainEmbed] });
}

// Slash command handlers
async function handleSlashUserInfo(interaction) {
    try {
        // Defer reply to prevent timeout
        await interaction.deferReply();

        const user = interaction.options.getUser('user') || interaction.user;

        // Check if guild exists
        if (!interaction.guild) {
            return await interaction.editReply({ content: `${global.emojiReplacer ? global.emojiReplacer.getError() : '❌'} This command can only be used in a server.` });
        }

        let member;
        try {
            // Try to get member from cache first
            member = interaction.guild.members.cache.get(user.id);

            // If not in cache, try to fetch
            if (!member) {
                member = await interaction.guild.members.fetch(user.id);
            }
        } catch (error) {
            console.error('Failed to fetch member:', error);
            return await interaction.editReply({ content: '❌ User not found in this server.' });
        }

        if (!member) {
            return await interaction.editReply({ content: '❌ User not found in this server.' });
        }

        const userData = await Database.getUserXP(user.id, interaction.guild.id);

        const embed = new EmbedBuilder()
            .setColor(0x0099FF)
            .setTitle(`User Info - ${user.tag}`)
            .setThumbnail(user.displayAvatarURL({ dynamic: true }))
            .addFields(
                { name: 'ID', value: user.id, inline: true },
                { name: 'Nickname', value: member.nickname || 'None', inline: true },
                { name: 'Account Created', value: `<t:${Math.floor(user.createdTimestamp / 1000)}:R>`, inline: true },
                { name: 'Joined Server', value: member.joinedAt ? `<t:${Math.floor(member.joinedAt.getTime() / 1000)}:R>` : 'Unknown', inline: true },
                { name: 'Level', value: userData.level.toString(), inline: true },
                { name: 'XP', value: userData.xp.toString(), inline: true },
                { name: 'Roles', value: member.roles.cache.filter(role => role.name !== '@everyone').map(role => role.toString()).join(', ') || 'None', inline: false }
            )
            .setFooter({ text: `Requested by ${interaction.user.tag}` })
            .setTimestamp();

        await interaction.editReply({ embeds: [embed] });
    } catch (error) {
        console.error('UserInfo error:', error);
        if (!interaction.replied && !interaction.deferred) {
            await interaction.reply({ content: '❌ Failed to fetch user information.', flags: 64 });
        } else {
            await interaction.editReply({ content: '❌ Failed to fetch user information.' });
        }
    }
}

async function handleSlashServerInfo(interaction) {
    try {
        // Defer reply to prevent timeout
        await interaction.deferReply();

        const guild = interaction.guild;
        if (!guild) {
            return await interaction.editReply({ content: '❌ Could not fetch server information.' });
        }

        let owner;
        try {
            owner = await guild.fetchOwner();
        } catch (error) {
            console.error('Failed to fetch owner:', error);
            owner = { user: { tag: 'Unknown', id: guild.ownerId || 'Unknown' } };
        }

        const embed = new EmbedBuilder()
            .setColor(0x0099FF)
            .setTitle(`Server Info - ${guild.name}`)
            .setThumbnail(guild.iconURL({ dynamic: true }))
            .addFields(
                { name: 'ID', value: guild.id, inline: true },
                { name: 'Owner', value: owner.user.tag, inline: true },
                { name: 'Created', value: `<t:${Math.floor(guild.createdTimestamp / 1000)}:R>`, inline: true },
                { name: 'Members', value: guild.memberCount.toString(), inline: true },
                { name: 'Channels', value: guild.channels.cache.size.toString(), inline: true },
                { name: 'Roles', value: guild.roles.cache.size.toString(), inline: true },
                { name: 'Boost Level', value: guild.premiumTier.toString(), inline: true },
                { name: 'Boosts', value: guild.premiumSubscriptionCount?.toString() || '0', inline: true },
                { name: 'Verification Level', value: guild.verificationLevel.toString(), inline: true }
            )
            .setFooter({ text: `Requested by ${interaction.user.tag}` })
            .setTimestamp();

        await interaction.editReply({ embeds: [embed] });
    } catch (error) {
        console.error('ServerInfo error:', error);
        if (!interaction.replied && !interaction.deferred) {
            await interaction.reply({ content: '❌ Failed to fetch server information.', flags: 64 });
        } else {
            await interaction.editReply({ content: '❌ Failed to fetch server information.' });
        }
    }
}

async function handleSlashAvatar(interaction) {
    const user = interaction.options.getUser('user') || interaction.user;

    const embed = new EmbedBuilder()
        .setColor(0x0099FF)
        .setTitle(`${user.tag}'s Avatar`)
        .setImage(user.displayAvatarURL({ dynamic: true, size: 512 }))
        .setFooter({ text: `Requested by ${interaction.user.tag}` });

    await interaction.reply({ embeds: [embed] });
}

async function handleSlashPing(interaction) {
    await interaction.deferReply();

    const embed = new EmbedBuilder()
        .setColor(0x00FF00)
        .setTitle(`${global.emojiReplacer ? global.emojiReplacer.getPing() : '🏓'} Pong!`)
        .addFields(
            { name: 'API Latency', value: `${Math.round(interaction.client.ws.ping)}ms`, inline: true }
        );

    await interaction.editReply({ embeds: [embed] });
}

async function handleSlashHelp(interaction) {
    const emojis = global.emojiReplacer || {};
    const category = interaction.options.getString('category');

    if (category) {
        return await showCategoryHelp(interaction, category, emojis);
    }

    // Main beautiful organized help embed
    const helpEmbed = new EmbedBuilder()
        .setColor(0x00D4FF)
        .setTitle(`${emojis.getBot ? emojis.getBot() : '🤖'} **Shanta Somali Bot** - Command Center`)
        .setDescription(`${emojis.getWave ? emojis.getWave() : '👋'} **Welcome to the most advanced Discord bot!**\n\n${emojis.getInfo ? emojis.getInfo() : 'ℹ️'} **Choose a category below to explore commands:**`)
        .setThumbnail('https://cdn.discordapp.com/attachments/653195475201032202/1264287289517801493/logggopng.png')
        .addFields(
            {
                name: `${emojis.getShield ? emojis.getShield() : '🛡️'} **ADMINISTRATION**`,
                value: `${emojis.getArrow ? emojis.getArrow() : '▸'} Moderation & Server Management\n${emojis.getArrow ? emojis.getArrow() : '▸'} Anti-Spam & Security Systems\n${emojis.getArrow ? emojis.getArrow() : '▸'} Advanced Configuration Tools`,
                inline: true
            },
            {
                name: `${emojis.getUsers ? emojis.getUsers() : '👥'} **COMMUNITY**`,
                value: `${emojis.getArrow ? emojis.getArrow() : '▸'} Welcome & Goodbye Systems\n${emojis.getArrow ? emojis.getArrow() : '▸'} Role Management & Reactions\n${emojis.getArrow ? emojis.getArrow() : '▸'} Verification & Security`,
                inline: true
            },
            {
                name: `${emojis.getTicket ? emojis.getTicket() : '🎫'} **SUPPORT**`,
                value: `${emojis.getArrow ? emojis.getArrow() : '▸'} Advanced Ticket System\n${emojis.getArrow ? emojis.getArrow() : '▸'} Private Channels & Whitelist\n${emojis.getArrow ? emojis.getArrow() : '▸'} Staff Management Tools`,
                inline: true
            },
            {
                name: `${emojis.getTrophy ? emojis.getTrophy() : '🏆'} **ENGAGEMENT**`,
                value: `${emojis.getArrow ? emojis.getArrow() : '▸'} Leveling & XP System\n${emojis.getArrow ? emojis.getArrow() : '▸'} Economy & Rewards\n${emojis.getArrow ? emojis.getArrow() : '▸'} Leaderboards & Rankings`,
                inline: true
            },
            {
                name: `${emojis.getGift ? emojis.getGift() : '🎁'} **ENTERTAINMENT**`,
                value: `${emojis.getArrow ? emojis.getArrow() : '▸'} Fun Games & Activities\n${emojis.getArrow ? emojis.getArrow() : '▸'} Giveaways & Events\n${emojis.getArrow ? emojis.getArrow() : '▸'} Memes & Random Content`,
                inline: true
            },
            {
                name: `${emojis.getGear ? emojis.getGear() : '⚙️'} **UTILITIES**`,
                value: `${emojis.getArrow ? emojis.getArrow() : '▸'} Server & User Information\n${emojis.getArrow ? emojis.getArrow() : '▸'} Bot Status & Performance\n${emojis.getArrow ? emojis.getArrow() : '▸'} Logging & Analytics`,
                inline: true
            }
        )
        .addFields({
            name: `${emojis.getStar ? emojis.getStar() : '⭐'} **Quick Commands**`,
            value: `${emojis.getDot ? emojis.getDot() : '•'} \`/util help category:admin\` - Admin commands\n${emojis.getDot ? emojis.getDot() : '•'} \`/util help category:public\` - Public commands\n${emojis.getDot ? emojis.getDot() : '•'} \`/setup dashboard\` - Server setup\n${emojis.getDot ? emojis.getDot() : '•'} \`/util ping\` - Bot status`,
            inline: false
        })
        .setFooter({
            text: `${emojis.getPowered ? emojis.getPowered() : '⚡'} Powered by Shanta Somali • ${emojis.getOnline ? emojis.getOnline() : '🟢'} Online & Ready`,
            iconURL: 'https://cdn.discordapp.com/attachments/653195475201032202/1264287289517801493/logggopng.png'
        })
        .setTimestamp();

    await interaction.reply({ embeds: [helpEmbed] });
}

async function showCategoryHelp(interaction, category, emojis) {
    let embed;

    switch (category) {
        case 'admin':
            embed = new EmbedBuilder()
                .setColor(0xFF4444)
                .setTitle(`${emojis.getShield ? emojis.getShield() : '🛡️'} **ADMIN COMMANDS** - Server Management`)
                .setDescription(`${emojis.getAdmin ? emojis.getAdmin() : '👑'} **Administrator & Moderator Commands**\n${emojis.getWarning ? emojis.getWarning() : '⚠️'} *These commands require special permissions*`)
                .setThumbnail('https://cdn.discordapp.com/attachments/653195475201032202/1264287289517801493/logggopng.png')
                .addFields(
                    {
                        name: `${emojis.getHammer ? emojis.getHammer() : '🔨'} **Moderation**`,
                        value: `${emojis.getDot ? emojis.getDot() : '•'} \`/mod ban\` - Ban users from server\n${emojis.getDot ? emojis.getDot() : '•'} \`/mod kick\` - Kick users from server\n${emojis.getDot ? emojis.getDot() : '•'} \`/mod mute\` - Timeout users\n${emojis.getDot ? emojis.getDot() : '•'} \`/mod warn\` - Warn users\n${emojis.getDot ? emojis.getDot() : '•'} \`/mod purge\` - Delete messages`,
                        inline: false
                    },
                    {
                        name: `${emojis.getShield ? emojis.getShield() : '🛡️'} **Security & Anti-Spam**`,
                        value: `${emojis.getDot ? emojis.getDot() : '•'} \`/antispam enable\` - Enable anti-spam\n${emojis.getDot ? emojis.getDot() : '•'} \`/antispam disable\` - Disable anti-spam\n${emojis.getDot ? emojis.getDot() : '•'} \`/antispam status\` - Check status\n${emojis.getDot ? emojis.getDot() : '•'} \`/verification setup\` - Setup verification`,
                        inline: false
                    },
                    {
                        name: `${emojis.getSettings ? emojis.getSettings() : '⚙️'} **Server Setup**`,
                        value: `${emojis.getDot ? emojis.getDot() : '•'} \`/setup dashboard\` - Complete setup panel\n${emojis.getDot ? emojis.getDot() : '•'} \`/setup features\` - Enable/disable features\n${emojis.getDot ? emojis.getDot() : '•'} \`/setup channels\` - Set bot channels\n${emojis.getDot ? emojis.getDot() : '•'} \`/setup roles\` - Setup roles (1-4 per type)\n${emojis.getDot ? emojis.getDot() : '•'} \`/setup messages\` - Custom messages`,
                        inline: false
                    },
                    {
                        name: `${emojis.getRole ? emojis.getRole() : '🎭'} **Role Management**`,
                        value: `${emojis.getDot ? emojis.getDot() : '•'} \`/autorole add\` - Add auto roles\n${emojis.getDot ? emojis.getDot() : '•'} \`/reactionroles create\` - Create reaction roles\n${emojis.getDot ? emojis.getDot() : '•'} \`/giveaway start\` - Start giveaways`,
                        inline: false
                    }
                )
                .setFooter({ text: `${emojis.getAdmin ? emojis.getAdmin() : '👑'} Admin Commands • Requires Permissions`, iconURL: 'https://cdn.discordapp.com/attachments/653195475201032202/1264287289517801493/logggopng.png' })
                .setTimestamp();
            break;

        case 'public':
            embed = new EmbedBuilder()
                .setColor(0x44FF44)
                .setTitle(`${emojis.getUsers ? emojis.getUsers() : '👥'} **PUBLIC COMMANDS** - Everyone Can Use`)
                .setDescription(`${emojis.getWave ? emojis.getWave() : '👋'} **Commands Available to All Users**\n${emojis.getInfo ? emojis.getInfo() : 'ℹ️'} *No special permissions required*`)
                .setThumbnail('https://cdn.discordapp.com/attachments/653195475201032202/1264287289517801493/logggopng.png')
                .addFields(
                    {
                        name: `${emojis.getGear ? emojis.getGear() : '⚙️'} **Utility & Information**`,
                        value: `${emojis.getDot ? emojis.getDot() : '•'} \`/util userinfo\` - Get user information\n${emojis.getDot ? emojis.getDot() : '•'} \`/util serverinfo\` - Get server information\n${emojis.getDot ? emojis.getDot() : '•'} \`/util avatar\` - Get user avatar\n${emojis.getDot ? emojis.getDot() : '•'} \`/util ping\` - Check bot latency`,
                        inline: false
                    },
                    {
                        name: `${emojis.getTrophy ? emojis.getTrophy() : '🏆'} **Leveling & Economy**`,
                        value: `${emojis.getDot ? emojis.getDot() : '•'} \`/level rank\` - Check your rank\n${emojis.getDot ? emojis.getDot() : '•'} \`/leaderboard\` - View server leaderboard\n${emojis.getDot ? emojis.getDot() : '•'} \`/economy balance\` - Check balance\n${emojis.getDot ? emojis.getDot() : '•'} \`/economy daily\` - Daily rewards\n${emojis.getDot ? emojis.getDot() : '•'} \`/economy work\` - Work for coins`,
                        inline: false
                    },
                    {
                        name: `${emojis.getGift ? emojis.getGift() : '🎁'} **Fun & Entertainment**`,
                        value: `${emojis.getDot ? emojis.getDot() : '•'} \`/fun meme\` - Get random memes\n${emojis.getDot ? emojis.getDot() : '•'} \`/fun joke\` - Get random jokes\n${emojis.getDot ? emojis.getDot() : '•'} \`/fun 8ball\` - Magic 8-ball\n${emojis.getDot ? emojis.getDot() : '•'} \`/fun rps\` - Rock paper scissors\n${emojis.getDot ? emojis.getDot() : '•'} \`/fun cat\` - Random cat images`,
                        inline: false
                    }
                )
                .setFooter({ text: `${emojis.getUsers ? emojis.getUsers() : '👥'} Public Commands • No Permissions Required`, iconURL: 'https://cdn.discordapp.com/attachments/653195475201032202/1264287289517801493/logggopng.png' })
                .setTimestamp();
            break;

        case 'setup':
            embed = new EmbedBuilder()
                .setColor(0x9B59B6)
                .setTitle(`${emojis.getSettings ? emojis.getSettings() : '⚙️'} **SERVER SETUP SYSTEM** - Complete Server Control`)
                .setDescription(`${emojis.getGear ? emojis.getGear() : '🔧'} **Advanced Server Setup**\n${emojis.getAdmin ? emojis.getAdmin() : '👑'} *Complete control over bot features and settings*`)
                .setThumbnail('https://cdn.discordapp.com/attachments/653195475201032202/1264287289517801493/logggopng.png')
                .addFields(
                    {
                        name: `${emojis.getDashboard ? emojis.getDashboard() : '📊'} **Setup Dashboard**`,
                        value: `${emojis.getDot ? emojis.getDot() : '•'} \`/setup dashboard\` - Complete overview\n${emojis.getDot ? emojis.getDot() : '•'} Shows all enabled/disabled features\n${emojis.getDot ? emojis.getDot() : '•'} Lists configured channels and roles\n${emojis.getDot ? emojis.getDot() : '•'} Quick setup commands`,
                        inline: false
                    },
                    {
                        name: `${emojis.getToggle ? emojis.getToggle() : '🔄'} **Feature Management**`,
                        value: `${emojis.getDot ? emojis.getDot() : '•'} \`/setup features\` - Enable/disable features\n${emojis.getDot ? emojis.getDot() : '•'} Anti-spam, tickets, welcome, economy\n${emojis.getDot ? emojis.getDot() : '•'} Leveling, auto roles, logging, verification\n${emojis.getDot ? emojis.getDot() : '•'} Reaction roles, giveaway system`,
                        inline: false
                    },
                    {
                        name: `${emojis.getChannel ? emojis.getChannel() : '📺'} **Channel & Role Setup**`,
                        value: `${emojis.getDot ? emojis.getDot() : '•'} \`/setup channels\` - Set bot channels\n${emojis.getDot ? emojis.getDot() : '•'} \`/setup roles\` - Setup roles (1-4 per type)\n${emojis.getDot ? emojis.getDot() : '•'} Mod logs, welcome, tickets, verification\n${emojis.getDot ? emojis.getDot() : '•'} Admin, moderator, verified roles`,
                        inline: false
                    },
                    {
                        name: `${emojis.getCustom ? emojis.getCustom() : '🎨'} **Customization**`,
                        value: `${emojis.getDot ? emojis.getDot() : '•'} \`/setup messages\` - Custom messages\n${emojis.getDot ? emojis.getDot() : '•'} \`/setup export\` - Backup settings\n${emojis.getDot ? emojis.getDot() : '•'} \`/setup import\` - Restore settings`,
                        inline: false
                    }
                )
                .setFooter({ text: `${emojis.getSettings ? emojis.getSettings() : '⚙️'} Setup System • Administrator Only`, iconURL: 'https://cdn.discordapp.com/attachments/653195475201032202/1264287289517801493/logggopng.png' })
                .setTimestamp();
            break;

        default:
            return await interaction.reply({ content: `${emojis.getError ? emojis.getError() : '❌'} Invalid category selected.`, ephemeral: true });
    }

    await interaction.reply({ embeds: [embed] });
}