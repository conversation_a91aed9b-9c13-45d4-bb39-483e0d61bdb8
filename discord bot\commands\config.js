const { <PERSON>lash<PERSON>ommandBuilder, EmbedBuilder, PermissionFlagsBits, ChannelType, ActionRowBuilder, StringSelectMenuBuilder, ButtonBuilder, ButtonStyle } = require('discord.js');
const Database = require('../database/database');

module.exports = {
    data: {
        name: 'config',
        description: 'Complete server configuration system'
    },
    slashData: new SlashCommandBuilder()
        .setName('config')
        .setDescription('Complete server configuration system')
        .setDefaultMemberPermissions(PermissionFlagsBits.Administrator)
        .setDMPermission(false)
        .addSubcommand(subcommand =>
            subcommand
                .setName('dashboard')
                .setDescription('Open server configuration dashboard'))
        .addSubcommand(subcommand =>
            subcommand
                .setName('features')
                .setDescription('Enable/disable bot features')
                .addStringOption(option =>
                    option.setName('feature')
                        .setDescription('Feature to toggle')
                        .setRequired(true)
                        .addChoices(
                            { name: '🛡️ Anti-Spam System', value: 'antispam' },
                            { name: '🎫 Ticket System', value: 'tickets' },
                            { name: '👋 Welcome System', value: 'welcome' },
                            { name: '💰 Economy System', value: 'economy' },
                            { name: '🏆 Leveling System', value: 'leveling' },
                            { name: '🎭 Auto Roles', value: 'autoroles' },
                            { name: '🎉 Reaction Roles', value: 'reactionroles' },
                            { name: '📊 Logging System', value: 'logging' },
                            { name: '🔐 Verification System', value: 'verification' },
                            { name: '🎁 Giveaway System', value: 'giveaways' }
                        ))
                .addBooleanOption(option =>
                    option.setName('enabled')
                        .setDescription('Enable or disable the feature')
                        .setRequired(true)))
        .addSubcommand(subcommand =>
            subcommand
                .setName('channels')
                .setDescription('Configure bot channels')
                .addStringOption(option =>
                    option.setName('type')
                        .setDescription('Channel type to configure')
                        .setRequired(true)
                        .addChoices(
                            { name: '📊 Mod Logs', value: 'modlogs' },
                            { name: '💬 Message Logs', value: 'messagelogs' },
                            { name: '👥 Member Logs', value: 'memberlogs' },
                            { name: '🎤 Voice Logs', value: 'voicelogs' },
                            { name: '👋 Welcome Channel', value: 'welcome' },
                            { name: '🎫 Ticket Category', value: 'tickets' },
                            { name: '🔐 Verification Channel', value: 'verification' }
                        ))
                .addChannelOption(option =>
                    option.setName('channel')
                        .setDescription('Channel to set (leave empty to disable)')
                        .setRequired(false)))
        .addSubcommand(subcommand =>
            subcommand
                .setName('roles')
                .setDescription('Configure bot roles')
                .addStringOption(option =>
                    option.setName('type')
                        .setDescription('Role type to configure')
                        .setRequired(true)
                        .addChoices(
                            { name: '👑 Admin Roles', value: 'admin' },
                            { name: '🛡️ Moderator Roles', value: 'moderator' },
                            { name: '🎫 Ticket Helper Roles', value: 'tickethelper' },
                            { name: '🔐 Verified Role', value: 'verified' },
                            { name: '🎭 Auto Role', value: 'autorole' },
                            { name: '🔇 Muted Role', value: 'muted' }
                        ))
                .addRoleOption(option =>
                    option.setName('role')
                        .setDescription('Role to set (leave empty to remove)')
                        .setRequired(false)))
        .addSubcommand(subcommand =>
            subcommand
                .setName('messages')
                .setDescription('Configure bot messages')
                .addStringOption(option =>
                    option.setName('type')
                        .setDescription('Message type to configure')
                        .setRequired(true)
                        .addChoices(
                            { name: '👋 Welcome Message', value: 'welcome' },
                            { name: '👋 Goodbye Message', value: 'goodbye' },
                            { name: '🔐 Verification Message', value: 'verification' },
                            { name: '🎫 Ticket Welcome Message', value: 'ticketwelcome' }
                        ))
                .addStringOption(option =>
                    option.setName('message')
                        .setDescription('Custom message (use {user}, {server}, {member_count})')
                        .setRequired(true)))
        .addSubcommand(subcommand =>
            subcommand
                .setName('emojis')
                .setDescription('Configure custom emojis for bot responses')
                .addStringOption(option =>
                    option.setName('type')
                        .setDescription('Emoji type to configure')
                        .setRequired(true)
                        .addChoices(
                            { name: '✅ Success Emoji', value: 'success' },
                            { name: '❌ Error Emoji', value: 'error' },
                            { name: 'ℹ️ Info Emoji', value: 'info' },
                            { name: '⚠️ Warning Emoji', value: 'warning' },
                            { name: '🎫 Ticket Emoji', value: 'ticket' },
                            { name: '👋 Welcome Emoji', value: 'welcome' }
                        ))
                .addStringOption(option =>
                    option.setName('emoji')
                        .setDescription('Custom emoji (e.g., <:name:id> or 🎉)')
                        .setRequired(true)))
        .addSubcommand(subcommand =>
            subcommand
                .setName('reset')
                .setDescription('Reset server configuration')
                .addStringOption(option =>
                    option.setName('type')
                        .setDescription('What to reset')
                        .setRequired(true)
                        .addChoices(
                            { name: '🔄 All Settings', value: 'all' },
                            { name: '📊 Channels Only', value: 'channels' },
                            { name: '🎭 Roles Only', value: 'roles' },
                            { name: '💬 Messages Only', value: 'messages' },
                            { name: '🎨 Emojis Only', value: 'emojis' },
                            { name: '🔧 Features Only', value: 'features' }
                        )))
        .addSubcommand(subcommand =>
            subcommand
                .setName('export')
                .setDescription('Export server configuration'))
        .addSubcommand(subcommand =>
            subcommand
                .setName('import')
                .setDescription('Import server configuration')
                .addStringOption(option =>
                    option.setName('config_data')
                        .setDescription('Configuration data to import')
                        .setRequired(true))),

    async execute(interaction) {
        // Check permissions
        if (!interaction.member.permissions.has(PermissionFlagsBits.Administrator)) {
            const errorEmoji = global.emojiReplacer ? global.emojiReplacer.getError() : '❌';
            return await interaction.reply({
                content: `${errorEmoji} You need **Administrator** permission to use this command.`,
                flags: 64
            });
        }

        const subcommand = interaction.options.getSubcommand();
        const emojis = global.emojiReplacer || {};

        try {
            switch (subcommand) {
                case 'dashboard':
                    await this.handleDashboard(interaction, emojis);
                    break;
                case 'features':
                    await this.handleFeatures(interaction, emojis);
                    break;
                case 'channels':
                    await this.handleChannels(interaction, emojis);
                    break;
                case 'roles':
                    await this.handleRoles(interaction, emojis);
                    break;
                case 'messages':
                    await this.handleMessages(interaction, emojis);
                    break;
                case 'emojis':
                    await this.handleEmojis(interaction, emojis);
                    break;
                case 'reset':
                    await this.handleReset(interaction, emojis);
                    break;
                case 'export':
                    await this.handleExport(interaction, emojis);
                    break;
                case 'import':
                    await this.handleImport(interaction, emojis);
                    break;
                default:
                    await interaction.reply({
                        content: `${emojis.getError ? emojis.getError() : '❌'} Unknown subcommand.`,
                        flags: 64
                    });
            }
        } catch (error) {
            console.error('Config command error:', error);
            
            if (!interaction.replied && !interaction.deferred) {
                await interaction.reply({
                    content: `${emojis.getError ? emojis.getError() : '❌'} An error occurred while executing the command.`,
                    flags: 64
                });
            }
        }
    },

    async handleDashboard(interaction, emojis) {
        await interaction.deferReply({ flags: 64 });

        try {
            // Get current server configuration
            const config = await this.getServerConfig(interaction.guild.id);
            
            const embed = new EmbedBuilder()
                .setColor(0x00D4FF)
                .setTitle(`${emojis.getSettings ? emojis.getSettings() : '⚙️'} **${interaction.guild.name}** - Configuration Dashboard`)
                .setDescription(`${emojis.getAdmin ? emojis.getAdmin() : '👑'} **Complete server configuration control panel**\n\n${emojis.getInfo ? emojis.getInfo() : 'ℹ️'} Use the commands below to configure your server:`)
                .setThumbnail(interaction.guild.iconURL() || 'https://cdn.discordapp.com/attachments/653195475201032202/1264287289517801493/logggopng.png')
                .addFields(
                    {
                        name: `${emojis.getToggle ? emojis.getToggle() : '🔄'} **Bot Features Status**`,
                        value: this.getFeatureStatus(config, emojis),
                        inline: false
                    },
                    {
                        name: `${emojis.getChannel ? emojis.getChannel() : '📺'} **Configured Channels**`,
                        value: this.getChannelStatus(config, interaction.guild, emojis),
                        inline: true
                    },
                    {
                        name: `${emojis.getRole ? emojis.getRole() : '🎭'} **Configured Roles**`,
                        value: this.getRoleStatus(config, interaction.guild, emojis),
                        inline: true
                    },
                    {
                        name: `${emojis.getGear ? emojis.getGear() : '🔧'} **Quick Configuration**`,
                        value: `${emojis.getDot ? emojis.getDot() : '•'} \`/config features\` - Toggle features\n${emojis.getDot ? emojis.getDot() : '•'} \`/config channels\` - Set channels\n${emojis.getDot ? emojis.getDot() : '•'} \`/config roles\` - Set roles\n${emojis.getDot ? emojis.getDot() : '•'} \`/config messages\` - Custom messages\n${emojis.getDot ? emojis.getDot() : '•'} \`/config emojis\` - Custom emojis`,
                        inline: false
                    }
                )
                .setFooter({ 
                    text: `${emojis.getPowered ? emojis.getPowered() : '⚡'} Powered by Shanta Somali • Configuration Dashboard`, 
                    iconURL: 'https://cdn.discordapp.com/attachments/653195475201032202/1264287289517801493/logggopng.png'
                })
                .setTimestamp();

            await interaction.editReply({ embeds: [embed] });

        } catch (error) {
            console.error('Failed to show dashboard:', error);
            await interaction.editReply({
                content: `${emojis.getError ? emojis.getError() : '❌'} Failed to load configuration dashboard.`
            });
        }
    },

    async getServerConfig(guildId) {
        // Get configuration from database
        try {
            const config = await Database.getServerConfig(guildId) || {};
            return {
                features: config.features || {},
                channels: config.channels || {},
                roles: config.roles || {},
                messages: config.messages || {},
                emojis: config.emojis || {}
            };
        } catch (error) {
            console.error('Failed to get server config:', error);
            return {
                features: {},
                channels: {},
                roles: {},
                messages: {},
                emojis: {}
            };
        }
    },

    getFeatureStatus(config, emojis) {
        const features = [
            { name: 'Anti-Spam', key: 'antispam' },
            { name: 'Tickets', key: 'tickets' },
            { name: 'Welcome', key: 'welcome' },
            { name: 'Economy', key: 'economy' },
            { name: 'Leveling', key: 'leveling' },
            { name: 'Auto Roles', key: 'autoroles' },
            { name: 'Logging', key: 'logging' }
        ];

        return features.map(feature => {
            const enabled = config.features[feature.key] !== false;
            const status = enabled ? `${emojis.getSuccess ? emojis.getSuccess() : '✅'}` : `${emojis.getError ? emojis.getError() : '❌'}`;
            return `${status} **${feature.name}**`;
        }).join('\n');
    },

    getChannelStatus(config, guild, emojis) {
        const channels = [
            { name: 'Mod Logs', key: 'modlogs' },
            { name: 'Welcome', key: 'welcome' },
            { name: 'Tickets', key: 'tickets' },
            { name: 'Verification', key: 'verification' }
        ];

        return channels.map(channel => {
            const channelId = config.channels[channel.key];
            if (channelId) {
                const ch = guild.channels.cache.get(channelId);
                return `${emojis.getSuccess ? emojis.getSuccess() : '✅'} **${channel.name}:** ${ch ? ch.toString() : 'Not found'}`;
            }
            return `${emojis.getError ? emojis.getError() : '❌'} **${channel.name}:** Not set`;
        }).join('\n');
    },

    getRoleStatus(config, guild, emojis) {
        const roles = [
            { name: 'Admin', key: 'admin' },
            { name: 'Moderator', key: 'moderator' },
            { name: 'Verified', key: 'verified' },
            { name: 'Auto Role', key: 'autorole' }
        ];

        return roles.map(role => {
            const roleId = config.roles[role.key];
            if (roleId) {
                const r = guild.roles.cache.get(roleId);
                return `${emojis.getSuccess ? emojis.getSuccess() : '✅'} **${role.name}:** ${r ? r.toString() : 'Not found'}`;
            }
            return `${emojis.getError ? emojis.getError() : '❌'} **${role.name}:** Not set`;
        }).join('\n');
    },

    async handleFeatures(interaction, emojis) {
        await interaction.deferReply({ flags: 64 });

        const feature = interaction.options.getString('feature');
        const enabled = interaction.options.getBoolean('enabled');

        try {
            // Update feature in database
            await Database.updateServerFeature(interaction.guild.id, feature, enabled);

            const featureNames = {
                antispam: '🛡️ Anti-Spam System',
                tickets: '🎫 Ticket System',
                welcome: '👋 Welcome System',
                economy: '💰 Economy System',
                leveling: '🏆 Leveling System',
                autoroles: '🎭 Auto Roles',
                reactionroles: '🎉 Reaction Roles',
                logging: '📊 Logging System',
                verification: '🔐 Verification System',
                giveaways: '🎁 Giveaway System'
            };

            const status = enabled ? 'ENABLED' : 'DISABLED';
            const statusEmoji = enabled ? emojis.getSuccess ? emojis.getSuccess() : '✅' : emojis.getError ? emojis.getError() : '❌';

            const embed = new EmbedBuilder()
                .setColor(enabled ? 0x00FF00 : 0xFF0000)
                .setTitle(`${statusEmoji} **Feature ${status}**`)
                .setDescription(`**${featureNames[feature]}** has been **${status.toLowerCase()}** for this server.`)
                .addFields({
                    name: `${emojis.getInfo ? emojis.getInfo() : 'ℹ️'} What this means`,
                    value: enabled
                        ? `• The ${featureNames[feature]} is now active\n• All related commands will work\n• Features will be available to users`
                        : `• The ${featureNames[feature]} is now inactive\n• Related commands will be disabled\n• Users won't be able to use this feature`,
                    inline: false
                })
                .setFooter({ text: `Feature: ${feature} • Status: ${status}` })
                .setTimestamp();

            await interaction.editReply({ embeds: [embed] });

        } catch (error) {
            console.error('Failed to update feature:', error);
            await interaction.editReply({
                content: `${emojis.getError ? emojis.getError() : '❌'} Failed to update feature setting.`
            });
        }
    },

    async handleChannels(interaction, emojis) {
        await interaction.deferReply({ flags: 64 });

        const type = interaction.options.getString('type');
        const channel = interaction.options.getChannel('channel');

        try {
            // Update channel in database
            await Database.updateServerChannel(interaction.guild.id, type, channel?.id || null);

            const channelNames = {
                modlogs: '📊 Moderation Logs',
                messagelogs: '💬 Message Logs',
                memberlogs: '👥 Member Logs',
                voicelogs: '🎤 Voice Logs',
                welcome: '👋 Welcome Channel',
                tickets: '🎫 Ticket Category',
                verification: '🔐 Verification Channel'
            };

            const embed = new EmbedBuilder()
                .setColor(channel ? 0x00FF00 : 0xFF0000)
                .setTitle(`${emojis.getSettings ? emojis.getSettings() : '⚙️'} **Channel Configuration Updated**`)
                .setDescription(`**${channelNames[type]}** has been ${channel ? 'set' : 'disabled'}.`)
                .addFields({
                    name: `${emojis.getChannel ? emojis.getChannel() : '📺'} Channel Setting`,
                    value: channel ? `Set to: ${channel}` : 'Disabled (no channel set)',
                    inline: false
                })
                .setFooter({ text: `Channel Type: ${type}` })
                .setTimestamp();

            await interaction.editReply({ embeds: [embed] });

        } catch (error) {
            console.error('Failed to update channel:', error);
            await interaction.editReply({
                content: `${emojis.getError ? emojis.getError() : '❌'} Failed to update channel setting.`
            });
        }
    },

    async handleRoles(interaction, emojis) {
        await interaction.deferReply({ flags: 64 });

        const type = interaction.options.getString('type');
        const role = interaction.options.getRole('role');

        try {
            // Update role in database
            await Database.updateServerRole(interaction.guild.id, type, role?.id || null);

            const roleNames = {
                admin: '👑 Admin Roles',
                moderator: '🛡️ Moderator Roles',
                tickethelper: '🎫 Ticket Helper Roles',
                verified: '🔐 Verified Role',
                autorole: '🎭 Auto Role',
                muted: '🔇 Muted Role'
            };

            const embed = new EmbedBuilder()
                .setColor(role ? 0x00FF00 : 0xFF0000)
                .setTitle(`${emojis.getRole ? emojis.getRole() : '🎭'} **Role Configuration Updated**`)
                .setDescription(`**${roleNames[type]}** has been ${role ? 'set' : 'removed'}.`)
                .addFields({
                    name: `${emojis.getRole ? emojis.getRole() : '🎭'} Role Setting`,
                    value: role ? `Set to: ${role}` : 'Removed (no role set)',
                    inline: false
                })
                .setFooter({ text: `Role Type: ${type}` })
                .setTimestamp();

            await interaction.editReply({ embeds: [embed] });

        } catch (error) {
            console.error('Failed to update role:', error);
            await interaction.editReply({
                content: `${emojis.getError ? emojis.getError() : '❌'} Failed to update role setting.`
            });
        }
    },

    async handleMessages(interaction, emojis) {
        await interaction.deferReply({ flags: 64 });

        const type = interaction.options.getString('type');
        const message = interaction.options.getString('message');

        try {
            // Update message in database
            await Database.updateServerMessage(interaction.guild.id, type, message);

            const messageNames = {
                welcome: '👋 Welcome Message',
                goodbye: '👋 Goodbye Message',
                verification: '🔐 Verification Message',
                ticketwelcome: '🎫 Ticket Welcome Message'
            };

            // Format message preview
            const preview = this.formatMessage(message, interaction.user, interaction.guild);

            const embed = new EmbedBuilder()
                .setColor(0x00FF00)
                .setTitle(`${emojis.getMessage ? emojis.getMessage() : '💬'} **Message Configuration Updated**`)
                .setDescription(`**${messageNames[type]}** has been updated.`)
                .addFields(
                    {
                        name: `${emojis.getPreview ? emojis.getPreview() : '👁️'} Message Preview`,
                        value: preview,
                        inline: false
                    },
                    {
                        name: `${emojis.getInfo ? emojis.getInfo() : 'ℹ️'} Available Variables`,
                        value: '`{user}` - User mention\n`{server}` - Server name\n`{member_count}` - Member count',
                        inline: false
                    }
                )
                .setFooter({ text: `Message Type: ${type}` })
                .setTimestamp();

            await interaction.editReply({ embeds: [embed] });

        } catch (error) {
            console.error('Failed to update message:', error);
            await interaction.editReply({
                content: `${emojis.getError ? emojis.getError() : '❌'} Failed to update message setting.`
            });
        }
    },

    formatMessage(message, user, guild) {
        if (!message) return 'No message set';

        return message
            .replace(/{user}/g, user.toString())
            .replace(/{server}/g, guild.name)
            .replace(/{member_count}/g, guild.memberCount.toString());
    },

    async handleEmojis(interaction, emojis) {
        await interaction.deferReply({ flags: 64 });

        const type = interaction.options.getString('type');
        const emoji = interaction.options.getString('emoji');

        try {
            // Update emoji in database
            await Database.updateServerEmoji(interaction.guild.id, type, emoji);

            const emojiNames = {
                success: '✅ Success Emoji',
                error: '❌ Error Emoji',
                info: 'ℹ️ Info Emoji',
                warning: '⚠️ Warning Emoji',
                ticket: '🎫 Ticket Emoji',
                welcome: '👋 Welcome Emoji'
            };

            const embed = new EmbedBuilder()
                .setColor(0x00FF00)
                .setTitle(`${emojis.getEmoji ? emojis.getEmoji() : '🎨'} **Emoji Configuration Updated**`)
                .setDescription(`**${emojiNames[type]}** has been updated.`)
                .addFields({
                    name: `${emojis.getPreview ? emojis.getPreview() : '👁️'} Emoji Preview`,
                    value: `New emoji: ${emoji}`,
                    inline: false
                })
                .setFooter({ text: `Emoji Type: ${type}` })
                .setTimestamp();

            await interaction.editReply({ embeds: [embed] });

        } catch (error) {
            console.error('Failed to update emoji:', error);
            await interaction.editReply({
                content: `${emojis.getError ? emojis.getError() : '❌'} Failed to update emoji setting.`
            });
        }
    },

    async handleReset(interaction, emojis) {
        await interaction.deferReply({ flags: 64 });

        const type = interaction.options.getString('type');

        try {
            // Reset configuration in database
            await Database.resetServerConfig(interaction.guild.id, type);

            const resetNames = {
                all: '🔄 All Settings',
                channels: '📊 Channels Only',
                roles: '🎭 Roles Only',
                messages: '💬 Messages Only',
                emojis: '🎨 Emojis Only',
                features: '🔧 Features Only'
            };

            const embed = new EmbedBuilder()
                .setColor(0xFF4444)
                .setTitle(`${emojis.getReset ? emojis.getReset() : '🔄'} **Configuration Reset**`)
                .setDescription(`**${resetNames[type]}** has been reset to default values.`)
                .addFields({
                    name: `${emojis.getWarning ? emojis.getWarning() : '⚠️'} What was reset`,
                    value: type === 'all'
                        ? 'All server configuration has been reset to defaults'
                        : `${resetNames[type]} configuration has been reset`,
                    inline: false
                })
                .setFooter({ text: `Reset Type: ${type}` })
                .setTimestamp();

            await interaction.editReply({ embeds: [embed] });

        } catch (error) {
            console.error('Failed to reset config:', error);
            await interaction.editReply({
                content: `${emojis.getError ? emojis.getError() : '❌'} Failed to reset configuration.`
            });
        }
    },

    async handleExport(interaction, emojis) {
        await interaction.deferReply({ flags: 64 });

        try {
            // Get current configuration
            const config = await this.getServerConfig(interaction.guild.id);

            // Create export data
            const exportData = {
                server: interaction.guild.name,
                exported: new Date().toISOString(),
                config: config
            };

            const configString = JSON.stringify(exportData, null, 2);

            const embed = new EmbedBuilder()
                .setColor(0x00FF00)
                .setTitle(`${emojis.getExport ? emojis.getExport() : '📤'} **Configuration Exported**`)
                .setDescription(`Server configuration has been exported successfully.`)
                .addFields({
                    name: `${emojis.getInfo ? emojis.getInfo() : 'ℹ️'} Export Information`,
                    value: `• Server: ${interaction.guild.name}\n• Exported: ${new Date().toLocaleString()}\n• Size: ${configString.length} characters`,
                    inline: false
                })
                .setFooter({ text: 'Copy the configuration data below to import to another server' })
                .setTimestamp();

            await interaction.editReply({
                embeds: [embed],
                content: `\`\`\`json\n${configString}\`\`\``
            });

        } catch (error) {
            console.error('Failed to export config:', error);
            await interaction.editReply({
                content: `${emojis.getError ? emojis.getError() : '❌'} Failed to export configuration.`
            });
        }
    },

    async handleImport(interaction, emojis) {
        await interaction.deferReply({ flags: 64 });

        const configData = interaction.options.getString('config_data');

        try {
            // Parse configuration data
            const importData = JSON.parse(configData);

            if (!importData.config) {
                throw new Error('Invalid configuration format');
            }

            // Import configuration to database
            await Database.importServerConfig(interaction.guild.id, importData.config);

            const embed = new EmbedBuilder()
                .setColor(0x00FF00)
                .setTitle(`${emojis.getImport ? emojis.getImport() : '📥'} **Configuration Imported**`)
                .setDescription(`Server configuration has been imported successfully.`)
                .addFields({
                    name: `${emojis.getInfo ? emojis.getInfo() : 'ℹ️'} Import Information`,
                    value: `• Original Server: ${importData.server || 'Unknown'}\n• Exported: ${importData.exported ? new Date(importData.exported).toLocaleString() : 'Unknown'}\n• Imported to: ${interaction.guild.name}`,
                    inline: false
                })
                .setFooter({ text: 'Configuration has been applied to this server' })
                .setTimestamp();

            await interaction.editReply({ embeds: [embed] });

        } catch (error) {
            console.error('Failed to import config:', error);
            await interaction.editReply({
                content: `${emojis.getError ? emojis.getError() : '❌'} Failed to import configuration. Please check the format.`
            });
        }
    }
};
