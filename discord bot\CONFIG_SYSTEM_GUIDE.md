# ⚙️ **Server Configuration System** - Complete Guide

## 🎯 **Overview**
The `/config` command provides a comprehensive server configuration system that allows administrators to customize every aspect of the bot for their server.

---

## 🔧 **Main Commands**

### **📊 Configuration Dashboard**
```
/config dashboard
```
- Shows complete server configuration overview
- Displays enabled/disabled features
- Lists configured channels and roles
- Provides quick setup commands

---

## 🎛️ **Feature Management**

### **Enable/Disable Bot Features**
```
/config features <feature> <enabled>
```

**Available Features:**
- 🛡️ **Anti-Spam System** - Automatic spam protection
- 🎫 **Ticket System** - Support ticket management
- 👋 **Welcome System** - Welcome/goodbye messages
- 💰 **Economy System** - Coin system and rewards
- 🏆 **Leveling System** - XP and ranking system
- 🎭 **Auto Roles** - Automatic role assignment
- 🎉 **Reaction Roles** - Role assignment via reactions
- 📊 **Logging System** - Activity logging
- 🔐 **Verification System** - Member verification
- 🎁 **Giveaway System** - Event management

**Examples:**
```
/config features feature:antispam enabled:true
/config features feature:tickets enabled:false
/config features feature:economy enabled:true
```

---

## 📺 **Channel Configuration**

### **Set Bot Channels**
```
/config channels <type> [channel]
```

**Available Channel Types:**
- 📊 **Mod Logs** - Moderation action logs
- 💬 **Message Logs** - Message edit/delete logs
- 👥 **Member Logs** - Join/leave logs
- 🎤 **Voice Logs** - Voice channel activity
- 👋 **Welcome Channel** - Welcome/goodbye messages
- 🎫 **Ticket Category** - Ticket channel category
- 🔐 **Verification Channel** - Member verification

**Examples:**
```
/config channels type:modlogs channel:#mod-logs
/config channels type:welcome channel:#welcome
/config channels type:tickets channel:#tickets
```

**To disable a channel:**
```
/config channels type:modlogs
```
*(Leave channel parameter empty)*

---

## 🎭 **Role Configuration**

### **Set Bot Roles**
```
/config roles <type> [role]
```

**Available Role Types:**
- 👑 **Admin Roles** - Administrator permissions
- 🛡️ **Moderator Roles** - Moderation permissions
- 🎫 **Ticket Helper Roles** - Ticket support staff
- 🔐 **Verified Role** - Verified member role
- 🎭 **Auto Role** - New member auto role
- 🔇 **Muted Role** - Timeout/mute role

**Examples:**
```
/config roles type:admin role:@Administrators
/config roles type:moderator role:@Moderators
/config roles type:verified role:@Verified
```

**To remove a role:**
```
/config roles type:admin
```
*(Leave role parameter empty)*

---

## 💬 **Message Customization**

### **Custom Bot Messages**
```
/config messages <type> <message>
```

**Available Message Types:**
- 👋 **Welcome Message** - New member welcome
- 👋 **Goodbye Message** - Member leave message
- 🔐 **Verification Message** - Verification prompt
- 🎫 **Ticket Welcome Message** - Ticket creation message

**Available Variables:**
- `{user}` - User mention (@username)
- `{server}` - Server name
- `{member_count}` - Current member count

**Examples:**
```
/config messages type:welcome message:Welcome {user} to **{server}**! 🎉 You are member #{member_count}!

/config messages type:goodbye message:Goodbye **{user}**, thanks for being part of **{server}**! 👋

/config messages type:verification message:Welcome to {server}! Please verify yourself to gain access to all channels.
```

---

## 🎨 **Custom Emojis**

### **Bot Response Emojis**
```
/config emojis <type> <emoji>
```

**Available Emoji Types:**
- ✅ **Success Emoji** - Success messages
- ❌ **Error Emoji** - Error messages
- ℹ️ **Info Emoji** - Information messages
- ⚠️ **Warning Emoji** - Warning messages
- 🎫 **Ticket Emoji** - Ticket system
- 👋 **Welcome Emoji** - Welcome messages

**Examples:**
```
/config emojis type:success emoji:✅
/config emojis type:error emoji:❌
/config emojis type:ticket emoji:<:ticket:123456789>
```

---

## 🔄 **Configuration Management**

### **Reset Configuration**
```
/config reset <type>
```

**Reset Options:**
- 🔄 **All Settings** - Reset everything to defaults
- 📊 **Channels Only** - Reset channel configurations
- 🎭 **Roles Only** - Reset role configurations
- 💬 **Messages Only** - Reset custom messages
- 🎨 **Emojis Only** - Reset custom emojis
- 🔧 **Features Only** - Reset feature toggles

**Examples:**
```
/config reset type:all
/config reset type:channels
/config reset type:features
```

### **Export Configuration**
```
/config export
```
- Exports current server configuration
- Provides JSON data for backup
- Can be imported to other servers

### **Import Configuration**
```
/config import <config_data>
```
- Imports configuration from JSON data
- Applies settings to current server
- Useful for server templates

---

## 🎯 **Quick Setup Examples**

### **Basic Server Setup**
```bash
# 1. Enable core features
/config features feature:antispam enabled:true
/config features feature:welcome enabled:true
/config features feature:logging enabled:true

# 2. Set essential channels
/config channels type:modlogs channel:#mod-logs
/config channels type:welcome channel:#welcome

# 3. Configure roles
/config roles type:admin role:@Administrators
/config roles type:moderator role:@Moderators

# 4. Custom welcome message
/config messages type:welcome message:Welcome {user} to **{server}**! 🎉
```

### **Advanced Server Setup**
```bash
# 1. Enable all features
/config features feature:tickets enabled:true
/config features feature:economy enabled:true
/config features feature:leveling enabled:true
/config features feature:verification enabled:true

# 2. Set all channels
/config channels type:tickets channel:#tickets
/config channels type:verification channel:#verification
/config channels type:memberlogs channel:#member-logs

# 3. Configure verification
/config roles type:verified role:@Verified
/config messages type:verification message:Please verify to access {server}!

# 4. Custom emojis
/config emojis type:success emoji:<:success:123>
/config emojis type:ticket emoji:<:ticket:456>
```

---

## 📋 **Configuration Dashboard Example**

When you run `/config dashboard`, you'll see:

```
⚙️ ServerName - Configuration Dashboard
👑 Complete server configuration control panel

🔄 Bot Features Status
✅ Anti-Spam    ❌ Tickets      ✅ Welcome
✅ Economy      ✅ Leveling     ❌ Auto Roles
✅ Logging      ❌ Verification ✅ Giveaways

📺 Configured Channels          🎭 Configured Roles
✅ Mod Logs: #mod-logs          ✅ Admin: @Administrators
✅ Welcome: #welcome            ✅ Moderator: @Moderators
❌ Tickets: Not set             ❌ Verified: Not set
❌ Verification: Not set        ❌ Auto Role: Not set

🔧 Quick Configuration
• /config features - Toggle features
• /config channels - Set channels
• /config roles - Set roles
• /config messages - Custom messages
• /config emojis - Custom emojis
```

---

## 🎉 **Benefits**

### **✅ Complete Control**
- Enable/disable any bot feature per server
- Customize every aspect of bot behavior
- Server-specific configurations

### **✅ Easy Management**
- Beautiful dashboard overview
- Simple toggle commands
- Export/import for backups

### **✅ Professional Setup**
- Custom messages with variables
- Custom emojis for branding
- Organized channel structure

---

## 🔒 **Permissions Required**
- **Administrator** permission required for all `/config` commands
- Ensures only server owners/admins can modify configuration
- Protects server settings from unauthorized changes

---

**🎉 Powered by Shanta Somali • Complete Server Configuration System**
